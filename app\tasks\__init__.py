from celery import Celery
from config import cfg
from celery.signals import task_prerun
import logging
import os

# 设置日志
logger = logging.getLogger(__name__)

# 创建 Celery 应用实例 - 执行 Worker
celery_app = Celery(
    'meeting_api_executor',
    broker=cfg.CELERY.BROKER_URL,
    backend=cfg.CELERY.RESULT_BACKEND,
    include=['app.tasks.funasr']
)

# 配置 Celery
celery_app.conf.update(
    # task_serializer=cfg.CELERY.TASK_SERIALIZER,
    # result_serializer=cfg.CELERY.RESULT_SERIALIZER,
    accept_content=cfg.CELERY.ACCEPT_CONTENT,
    timezone=cfg.CELERY.TIMEZONE,
    enable_utc=cfg.CELERY.ENABLE_UTC,
    task_track_started=cfg.CELERY.TASK_TRACK_STARTED,
    task_time_limit=cfg.CELERY.TASK_TIME_LIMIT,
    task_soft_time_limit=cfg.CELERY.TASK_SOFT_TIME_LIMIT,
)

# Worker 启动时的回调函数
@celery_app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """Worker 启动时的配置函数"""
    print("🚀 Celery 执行 Worker 配置完成")

@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kw):
    """任务发送到队列时的处理 - 专注于状态更新和日志记录"""
    print("task_prerun_handler")