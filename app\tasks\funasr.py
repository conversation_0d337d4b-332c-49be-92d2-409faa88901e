import os
import time
import requests
import urllib.parse
from pathlib import Path
from funasr import AutoModel
from config import cfg
from app.tasks.time_tracker import TimeTracker
from app.tasks import celery_app
from app.tasks.file_merger import FileMerger

# 全局变量
funasr_model = None
is_init_model = False

def get_or_init_model():
    """获取或初始化模型"""
    global funasr_model
    global is_init_model
    
    if funasr_model is None or not is_init_model:
        funasr_model = init_model()
        is_init_model = True
    
    return funasr_model

def process_single_file(audio_file):
    """处理单个音频文件"""
    # 获取模型（如果未初始化会自动初始化）
    model = get_or_init_model()
    
    print(f"\n🎵 开始处理: {os.path.basename(audio_file)}")
    
    # 获取文件信息
    file_size = os.path.getsize(audio_file) / (1024 * 1024)  # MB
    print(f"📊 文件大小: {file_size:.2f} MB")
    
    # 记录转写开始时间
    transcription_start = time.time()
    
    try:
        results = model.generate(
            input=audio_file, 
            batch_size_s=300, # cfg.FUNASR.BATCH_SIZE_S, 
            # hotword=cfg.hotwords[0]
        )
        # 记录转写结束时间
        transcription_time = time.time() - transcription_start
        
        # 处理结果
        transcription_result = {
            "file_path": audio_file,
            "file_name": os.path.basename(audio_file),
            "file_size_mb": file_size,
            "speaker_segments": [],
            "statistics": {
                "transcription_time": transcription_time,
            }
        }

        if results and len(results) > 0:
            result = results[0]
            sentence_info = result.get("sentence_info", [])
            for sentence in sentence_info:
                segment = {
                    "start_time": sentence.get("start", 0) / 1004.0,
                    "end_time": sentence.get("end", 0) / 1000.0,
                    "speaker": f"{sentence.get('spk', 0)}",
                    "text": sentence.get("text", "")
                }
                transcription_result["speaker_segments"].append(segment)
        
        # 显示结果摘要
        speaker_count = len(set([s['speaker'] for s in transcription_result["speaker_segments"]]))
        total_duration = max([s["end_time"] for s in transcription_result["speaker_segments"]]) if transcription_result["speaker_segments"] else 0
        
        print(f"✅ 转写完成:")
        print(f"   - 说话人数量: {speaker_count}")
        print(f"   - 音频时长: {total_duration:.1f}秒")
        print(f"   - 转写时间: {transcription_time:.2f}秒")
        if total_duration > 0:
            rtf = transcription_time / total_duration
            print(f"   - RTF: {rtf:.2f}")

        return transcription_result
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return None
    
def init_model():
    """初始化模型"""
    global is_init_model
    is_init_model = True
    
    # 创建时间跟踪器
    tracker = TimeTracker("FunASR 模型预热")
    tracker.start()
    
 
    # 阶段1: 模型初始化
    tracker.start_stage("模型初始化")
    print("\n🚀 正在初始化模型...")
    
    model = AutoModel(
        model=cfg.FUNASR.MODEL,  
        vad_model=cfg.FUNASR.VAD_MODEL, 
        punc_model=cfg.FUNASR.PUNC_MODEL, 
        spk_model=cfg.FUNASR.SPK_MODEL,
        device="cuda",
        disable_update=True,
        return_raw_text=False,
        return_spk_res=True,
        sentence_timestamp=False,
    )
    tracker.end_stage()
    
    # 结束总计时
    tracker.end()
    # 打印详细统计
    tracker.print_summary()

    return model

def download_audio_files_and_merge(id: int, audio_origin_urls: list[str]):
    """
    下载音频文件并合并
    
    Args:
        id: 任务ID
        audio_origin_urls: 音频文件URL列表
        
    Returns:
        str: 合并后的本地文件路径
    """
    try:
        if not audio_origin_urls:
            raise ValueError("音频文件URL列表不能为空")
        
        # 如果只有一个文件，直接下载
        if len(audio_origin_urls) == 1:
            print(f"📁 单个文件，直接下载: {audio_origin_urls[0]}")
            return download_audio_file(id, audio_origin_urls[0])
        
        # 多个文件，需要下载并合并
        print(f"📁 多个文件，开始下载和合并: {len(audio_origin_urls)} 个文件")
        
        # 下载所有文件
        downloaded_files = []
        for i, url in enumerate(audio_origin_urls):
            print(f"⬇️ 下载文件 {i+1}/{len(audio_origin_urls)}: {url}")
            local_file = download_audio_file(id, url)
            downloaded_files.append(local_file)
        
        # 合并文件
        print(f"🔗 开始合并 {len(downloaded_files)} 个文件")
        merged_file = merge_audio_files(id, downloaded_files)
        
        # 清理下载的临时文件
        if cfg.CACHE.CLEAR_AFTER_USED:
            for file_path in downloaded_files:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        print(f"🗑️ 清理临时文件: {file_path}")
                except Exception as e:
                    print(f"⚠️ 清理临时文件失败: {file_path}, 错误: {e}")
        

        return merged_file
        
    except Exception as e:
        print(f"❌ 下载和合并音频文件失败: {e}")
        raise

def download_audio_file(id: int, audio_origin_url: str):
    """
    下载音频文件
    
    Args:
        id: 任务ID
        audio_origin_url: 音频文件URL或本地路径
        
    Returns:
        str: 本地文件路径
    """
    try:
        # 判断是否为本地路径
        parsed_url = urllib.parse.urlparse(audio_origin_url)
        
        # 如果是本地路径（没有scheme或scheme为file）
        if not parsed_url.scheme or parsed_url.scheme == 'file':
            local_path = parsed_url.path if parsed_url.scheme == 'file' else audio_origin_url
            
            # 检查文件是否存在
            if os.path.exists(local_path):
                print(f"📁 使用本地文件: {local_path}")
                return local_path
            else:
                raise FileNotFoundError(f"本地文件不存在: {local_path}")
        
        # 如果是远程URL，下载到缓存目录
        else:
            print(f"🌐 下载远程文件: {audio_origin_url}")
            
            # 获取系统临时目录作为缓存目录, 按日期为目录
            cache_dir = cfg.CACHE.DIR
            audio_cache_dir = os.path.join(cache_dir, "audio_cache", time.strftime("%Y-%m-%d"))
            
            # 创建音频缓存目录
            os.makedirs(audio_cache_dir, exist_ok=True)
            
            # 生成缓存文件名
            file_extension = get_file_extension(audio_origin_url)
            cache_filename = f"audio_{id}_{int(time.time())}{file_extension}"
            cache_file_path = os.path.join(audio_cache_dir, cache_filename)
            
            # 下载文件
            print(f"⬇️ 正在下载到: {cache_file_path}")
            response = requests.get(audio_origin_url, stream=True, timeout=600)
            response.raise_for_status()
            
            # 写入文件
            with open(cache_file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(cache_file_path) / (1024 * 1024)  # MB
            print(f"✅ 下载完成: {file_size:.2f} MB")
            
            return cache_file_path
            
    except Exception as e:
        print(f"❌ 下载音频文件失败: {e}")
        raise

def get_file_extension(url: str) -> str:
    """
    从URL中获取文件扩展名
    
    Args:
        url: 文件URL
        
    Returns:
        str: 文件扩展名
    """
    # 从URL中提取文件名
    filename = url.split('/')[-1]
    
    # 获取扩展名
    if '.' in filename:
        return '.' + filename.split('.')[-1]
    else:
        # 默认扩展名
        return '.mp3'

def merge_audio_files(id: int, audio_files: list[str]):
    """
    合并音频文件
    
    Args:
        id: 任务ID
        audio_files: 音频文件路径列表
        
    Returns:
        str: 合并后的文件路径
    """
    try:
        if not audio_files:
            raise ValueError("音频文件列表不能为空")

        # 判断音频的格式后缀是否一致
        file_extension = get_file_extension(audio_files[0])
        for audio_file in audio_files:
            if get_file_extension(audio_file) != file_extension:
                raise ValueError("音频文件格式不一致")
            

        # 获取系统临时目录
        temp_dir = cfg.CACHE.DIR
        audio_cache_dir = os.path.join(temp_dir, "audio_cache", time.strftime("%Y-%m-%d"))
        os.makedirs(audio_cache_dir, exist_ok=True)
        
        # 生成合并后的文件名
        merged_filename = f"merged_audio_{id}_{int(time.time())}.wav"
        merged_file_path = os.path.join(audio_cache_dir, merged_filename)
        
        print(f"🔗 合并文件到: {merged_file_path}")
        
        # 合并文件
        file_merger = FileMerger()
        merge_result = file_merger.merge_files_auto(audio_files, merged_file_path)

        if not merge_result["success"]:
            raise Exception(merge_result["error"])

        merged_size = os.path.getsize(merged_file_path) / (1024 * 1024)  
                
        # MB
        print(f"✅ 文件合并完成: {merged_size:.2f} MB")
        
        return merged_file_path
        
    except Exception as e:
        print(f"❌ 合并音频文件失败: {e}")
        raise