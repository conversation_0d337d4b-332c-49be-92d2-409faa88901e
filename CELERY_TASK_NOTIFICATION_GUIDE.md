# Celery任务执行状态通知指南

## 概述

本文档介绍如何在Celery任务执行结束后通知业务服务执行状态。系统使用Celery信号机制自动更新数据库状态，并提供API接口供业务服务查询任务状态。

## 通知机制

### 1. 自动数据库状态更新（核心机制）

系统使用Celery信号机制自动更新数据库中的任务状态，无需额外配置。

**工作原理：**
- 任务成功完成时，自动更新 `audioState` 为 `"finished"`
- 任务失败时，自动更新 `audioState` 为 `"failed"`
- 同时更新 `celeryTaskStatus` 和 `celeryTaskResult` 字段

**配置位置：** `app/tasks/__init__.py`

### 2. API状态查询

提供实时查询任务状态的API接口，支持轮询方式获取任务状态。

## API接口

### 1. 查询音频转换状态

```http
GET /api/meeting/audio-status/{meeting_id}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功!!!",
  "data": {
    "meeting_id": 123,
    "audio_state": "handing",
    "task_status": "progress",
    "progress": 50,
    "current_file": 2,
    "total_files": 4,
    "error_message": null,
    "celery_task_id": "celery-task-uuid",
    "celery_task_status": "running",
    "celery_task_result": null,
    "estimated_completion_time": "2024-01-01T12:30:00Z"
  }
}
```

### 2. 重试失败的任务

```http
POST /api/meeting/retry-task/{meeting_id}
Content-Type: application/json

{
  "force": false
}
```

### 3. 取消正在运行的任务

```http
POST /api/meeting/cancel-task/{meeting_id}
Content-Type: application/json

{
  "reason": "用户手动取消"
}
```

### 4. 获取任务历史

```http
GET /api/meeting/task-history/{meeting_id}
```

## 任务状态说明

### 音频状态 (audioState)
- `uploaded`: 已上传，等待处理
- `handing`: 处理中
- `finished`: 处理完成
- `failed`: 处理失败
- `canceled`: 已取消

### Celery任务状态 (celeryTaskStatus)
- `pending`: 等待中
- `running`: 运行中
- `finished`: 已完成
- `failed`: 失败
- `canceled`: 已取消

## 实现细节

### 1. Celery信号处理

系统使用以下Celery信号：

```python
@task_success.connect
def task_success_handler(sender=None, result=None, **kwargs):
    """任务成功完成后的处理"""

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, **kw):
    """任务失败后的处理"""

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, state=None, **kw):
    """任务执行完成后的处理（无论成功还是失败）"""
```

### 2. 数据库状态更新

任务完成后自动更新以下字段：
- `audioState`: 音频处理状态
- `celeryTaskStatus`: Celery任务状态
- `celeryTaskResult`: 任务结果（JSON格式）
- `updatedAt`: 更新时间

## 使用示例

### 1. 前端轮询状态

```javascript
// 轮询任务状态
async function pollTaskStatus(meetingId) {
  const interval = setInterval(async () => {
    try {
      const response = await fetch(`/api/meeting/audio-status/${meetingId}`);
      const data = await response.json();
      
      if (data.data.audio_state === 'finished') {
        clearInterval(interval);
        console.log('任务完成！');
        // 处理完成逻辑
      } else if (data.data.audio_state === 'failed') {
        clearInterval(interval);
        console.log('任务失败！');
        // 处理失败逻辑
      }
    } catch (error) {
      console.error('查询状态失败:', error);
    }
  }, 5000); // 每5秒查询一次
}
```

### 2. 手动重试失败任务

```python
import requests

# 重试失败的任务
def retry_failed_task(meeting_id, force=False):
    response = requests.post(
        f'/api/meeting/retry-task/{meeting_id}',
        json={'force': force}
    )
    return response.json()
```

### 3. 取消正在运行的任务

```python
import requests

# 取消任务
def cancel_task(meeting_id, reason="用户取消"):
    response = requests.post(
        f'/api/meeting/cancel-task/{meeting_id}',
        json={'reason': reason}
    )
    return response.json()
```

## 监控和调试

### 1. 查看Celery任务状态

```bash
# 查看活跃任务
celery -A app.tasks.celery_app inspect active

# 查看任务统计
celery -A app.tasks.celery_app inspect stats

# 查看任务历史
celery -A app.tasks.celery_app inspect reserved
```

### 2. 日志监控

系统会记录详细的任务执行日志：

```bash
# 查看任务相关日志
tail -f logs/celery.log | grep "任务"
```

### 3. 数据库查询

```sql
-- 查看失败的任务
SELECT id, name, audioState, celeryTaskStatus, celeryTaskResult 
FROM meetings 
WHERE audioState = 'failed';

-- 查看正在处理的任务
SELECT id, name, audioState, celeryTaskStatus, createdAt 
FROM meetings 
WHERE audioState = 'handing';

-- 查看已完成的任务
SELECT id, name, audioState, celeryTaskStatus, updatedAt 
FROM meetings 
WHERE audioState = 'finished';
```

## 最佳实践

### 1. 错误处理

- 始终检查任务状态，不要假设任务一定会成功
- 实现重试机制处理临时性失败
- 记录详细的错误信息便于调试

### 2. 性能优化

- 合理设置轮询间隔，建议5-10秒
- 实现任务进度显示提升用户体验
- 使用缓存减少数据库查询压力

### 3. 监控告警

- 监控失败任务数量
- 设置任务执行时间告警
- 监控Celery Worker状态

### 4. 安全考虑

- 限制API访问频率
- 保护敏感的任务结果数据
- 验证用户权限

## 故障排除

### 1. 任务状态不更新

**可能原因：**
- Celery Worker未正常运行
- 数据库连接问题
- 信号处理器异常

**解决方案：**
- 检查Celery Worker状态
- 查看错误日志
- 重启Celery Worker

### 2. 任务卡在运行状态

**可能原因：**
- Worker进程异常退出
- 任务执行时间过长
- 资源不足

**解决方案：**
- 重启Celery Worker
- 检查系统资源
- 调整任务超时设置

### 3. API查询返回错误

**可能原因：**
- 数据库连接问题
- 权限验证失败
- 会议记录不存在

**解决方案：**
- 检查数据库连接
- 验证用户权限
- 确认会议ID正确

## 总结

通过以上机制，系统能够可靠地通知业务服务Celery任务的执行状态：

1. **自动状态更新**：确保数据库状态始终准确
2. **API查询**：支持主动查询任务状态
3. **任务管理**：提供重试、取消等管理功能

这种简化的架构更加稳定可靠，减少了外部依赖，便于维护和部署。 