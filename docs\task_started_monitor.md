# 任务开始状态监听功能

## 概述

系统新增了任务开始状态的监听功能，当 Celery 任务开始执行时，会自动触发回调函数，更新数据库状态并记录任务开始信息。

## 功能特性

### 1. 自动状态更新
- 任务开始时自动将 `audioState` 更新为 `"handing"`
- 任务开始时自动将 `celeryTaskStatus` 更新为 `"running"`
- 记录任务开始时间、任务名称、参数等信息

### 2. 业务逻辑处理
- 支持针对不同任务类型的自定义业务逻辑
- 目前支持 `transcribe_audio` 任务的开始处理
- 可扩展支持其他任务类型

### 3. 详细日志记录
- 记录任务开始执行的详细信息
- 包含任务ID、任务名称、参数等
- 便于调试和监控

## 实现细节

### 1. 信号监听

使用 Celery 的 `task_started` 信号监听任务开始事件：

```python
from celery.signals import task_started

@task_started.connect
def task_started_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kw):
    """任务开始执行时的处理"""
    # 处理逻辑
```

### 2. 状态更新流程

```python
def update_task_status_in_db(task_id: str, state: str, result=None):
    """更新数据库中的任务状态"""
    if state == 'STARTED':
        meeting.audioState = "handing"
        meeting.celeryTaskStatus = "running"
        meeting.celeryTaskResult = json.dumps(result, ensure_ascii=False)
```

### 3. 业务逻辑处理

```python
def handle_transcribe_started(task_id: str, args: tuple, kwargs: dict):
    """处理音频转写任务开始 - 业务逻辑处理"""
    # 自定义业务逻辑
    start_info = {
        "started_at": datetime.utcnow().isoformat(),
        "task_id": task_id,
        "args": str(args),
        "kwargs": str(kwargs)
    }
    logger.info(f"会议 {meeting.id} 开始音频转写，开始信息: {start_info}")
```

## 任务状态流转

### 完整的状态流转图

```
任务提交 → pending → STARTED → running → SUCCESS → finished
                ↓         ↓         ↓         ↓
              pending   handing   running   finished
```

### 状态说明

| Celery 状态 | 数据库状态 | 说明 |
|------------|-----------|------|
| PENDING | pending | 任务等待中 |
| STARTED | running | 任务开始执行 |
| PROGRESS | running | 任务执行中 |
| SUCCESS | finished | 任务成功完成 |
| FAILURE | failed | 任务执行失败 |
| REVOKED | canceled | 任务被取消 |

## 配置要求

### 1. Celery 配置

确保 `task_track_started` 配置为 `True`：

```python
celery_app.conf.update(
    task_track_started=True,  # 启用任务开始跟踪
    # 其他配置...
)
```

### 2. 监听 Worker 配置

监听 Worker 需要加载 monitor 模块：

```python
monitor_app = Celery(
    'meeting_api_monitor',
    broker=cfg.CELERY.BROKER_URL,
    backend=cfg.CELERY.RESULT_BACKEND,
    include=['app.tasks.monitor']  # 包含监听模块
)
```

## 使用示例

### 1. 启动监听 Worker

```bash
# 启动监听 Worker
python scripts/start_monitor_worker.py

# 或者使用 Celery 命令
celery -A app.tasks.monitor.monitor_app worker --loglevel=info
```

### 2. 测试功能

```bash
# 运行测试脚本
python test_task_started_monitor.py
```

### 3. 查看日志

监听 Worker 会输出详细的日志信息：

```
🔍 Celery 监听 Worker 初始化中...
✅ 监听 Worker 准备就绪，开始监听任务状态
💡 注意：监听 Worker 不加载 FunASR 模型，只负责状态监听

[INFO] 任务开始执行: transcribe_audio, ID: abc123-def456
[INFO] 会议 123 开始音频转写，开始信息: {...}
[INFO] 任务 abc123-def456 状态已更新为: STARTED
```

## 监控和调试

### 1. 查看任务状态

通过 API 接口查看任务状态：

```http
GET /api/meeting/audio-status/{meeting_id}
```

### 2. 查看数据库状态

```sql
SELECT id, name, audioState, celeryTaskStatus, celeryTaskResult 
FROM meetings 
WHERE celeryTaskId = 'task_id';
```

### 3. 查看 Celery 任务状态

```python
from app.tasks.monitor import monitor_app

result = monitor_app.AsyncResult('task_id')
print(f"任务状态: {result.state}")
print(f"任务信息: {result.info}")
```

## 扩展支持

### 添加新的任务类型支持

1. 在 `task_started_handler` 中添加新的任务类型判断：

```python
if task_name == 'your_task_name':
    handle_your_task_started(task_id, args, kwargs)
```

2. 实现对应的处理函数：

```python
def handle_your_task_started(task_id: str, args: tuple, kwargs: dict):
    """处理自定义任务开始 - 业务逻辑处理"""
    # 自定义业务逻辑
    pass
```

## 注意事项

1. **监听 Worker 分离**：监听 Worker 与执行 Worker 分离，避免相互影响
2. **状态一致性**：确保数据库状态与 Celery 任务状态保持一致
3. **错误处理**：所有回调函数都包含异常处理，避免影响任务执行
4. **性能考虑**：监听操作应该轻量级，避免影响系统性能

## 故障排除

### 1. 任务开始状态未更新

- 检查监听 Worker 是否正常运行
- 检查 `task_track_started` 配置是否正确
- 查看监听 Worker 日志是否有错误信息

### 2. 数据库状态不一致

- 检查数据库连接是否正常
- 查看 `update_task_status_in_db` 函数的错误日志
- 验证会议记录是否存在

### 3. 业务逻辑未执行

- 检查任务名称是否正确
- 查看对应的处理函数是否有异常
- 验证数据库查询是否成功 