import time
import json
from datetime import datetime

class TimeTracker:
    """时间统计工具类"""
    
    def __init__(self, name="任务"):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.stages = {}
        self.current_stage = None
        self.stage_start_time = None
        
    def start(self):
        """开始计时"""
        self.start_time = time.time()
        print(f"🚀 开始执行: {self.name}")
        
    def end(self):
        """结束计时"""
        self.end_time = time.time()
        total_time = self.end_time - self.start_time
        print(f"✅ 执行完成: {self.name}, 总耗时: {self.format_time(total_time)}")
        return total_time
    
    def start_stage(self, stage_name):
        """开始一个阶段"""
        if self.current_stage:
            self.end_stage()
        
        self.current_stage = stage_name
        self.stage_start_time = time.time()
        print(f"📋 开始阶段: {stage_name}")
        
    def end_stage(self):
        """结束当前阶段"""
        if self.current_stage and self.stage_start_time:
            stage_time = time.time() - self.stage_start_time
            self.stages[self.current_stage] = stage_time
            print(f"✅ 阶段完成: {self.current_stage}, 耗时: {self.format_time(stage_time)}")
            self.current_stage = None
            self.stage_start_time = None
    
    def get_total_time(self):
        """获取总执行时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return time.time() - self.start_time
        return 0
    
    def get_stage_time(self, stage_name):
        """获取指定阶段的执行时间"""
        return self.stages.get(stage_name, 0)
    
    def format_time(self, seconds):
        """格式化时间显示"""
        if seconds < 60:
            return f"{seconds:.2f}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            remaining_seconds = seconds % 60
            return f"{minutes}分{remaining_seconds:.2f}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            remaining_seconds = seconds % 60
            return f"{hours}小时{minutes}分{remaining_seconds:.2f}秒"
    
    def print_summary(self):
        """打印时间统计摘要"""
        total_time = self.get_total_time()
        
        print(f"\n⏱️  {self.name} - 执行时间统计:")
        print("=" * 50)
        
        # 打印各阶段时间
        for stage_name, stage_time in self.stages.items():
            percentage = (stage_time / total_time * 100) if total_time > 0 else 0
            print(f"   {stage_name}: {self.format_time(stage_time)} ({percentage:.1f}%)")
        
        print(f"   总执行时间: {self.format_time(total_time)}")
        print("=" * 50)
    
    def get_statistics(self):
        """获取统计信息字典"""
        total_time = self.get_total_time()
        
        stats = {
            "task_name": self.name,
            "start_time": datetime.fromtimestamp(self.start_time).isoformat() if self.start_time else None,
            "end_time": datetime.fromtimestamp(self.end_time).isoformat() if self.end_time else None,
            "total_time": total_time,
            "stages": self.stages.copy(),
            "stage_percentages": {}
        }
        
        # 计算各阶段占比
        for stage_name, stage_time in self.stages.items():
            percentage = (stage_time / total_time * 100) if total_time > 0 else 0
            stats["stage_percentages"][stage_name] = percentage
        
        return stats
    
    def save_statistics(self, filename="execution_statistics.json"):
        """保存统计信息到文件"""
        stats = self.get_statistics()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        print(f"💾 执行统计已保存到: {filename}")

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.trackers = {}
    
    def create_tracker(self, name):
        """创建一个新的时间跟踪器"""
        tracker = TimeTracker(name)
        self.trackers[name] = tracker
        return tracker
    
    def get_tracker(self, name):
        """获取指定的时间跟踪器"""
        return self.trackers.get(name)
    
    def print_all_summaries(self):
        """打印所有跟踪器的摘要"""
        print("\n📊 所有任务执行统计:")
        print("=" * 60)
        
        for name, tracker in self.trackers.items():
            tracker.print_summary()
    
    def save_all_statistics(self, filename="all_execution_statistics.json"):
        """保存所有统计信息"""
        all_stats = {
            "summary": {
                "total_tasks": len(self.trackers),
                "total_execution_time": sum(t.get_total_time() for t in self.trackers.values())
            },
            "tasks": {name: tracker.get_statistics() for name, tracker in self.trackers.items()}
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(all_stats, f, ensure_ascii=False, indent=2)
        print(f"💾 所有执行统计已保存到: {filename}")

# 全局性能监控器实例
performance_monitor = PerformanceMonitor() 