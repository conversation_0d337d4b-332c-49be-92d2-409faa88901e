#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件合并工具 - 使用FFmpeg合并多个音频/视频文件
支持格式：mp4, wav, mp3, avi, mov, mkv等
支持输出为WAV格式
"""

import os
import subprocess
import json
import tempfile
from pathlib import Path
from typing import List, Dict, Optional, Union
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FileMerger:
    """文件合并器类"""
    
    def __init__(self, ffmpeg_path: str = "ffmpeg"):
        """
        初始化文件合并器
        
        Args:
            ffmpeg_path: FFmpeg可执行文件路径，默认为"ffmpeg"
        """
        self.ffmpeg_path = ffmpeg_path
        self._check_ffmpeg()
    
    def _check_ffmpeg(self) -> bool:
        """
        检查FFmpeg是否可用
        
        Returns:
            bool: FFmpeg是否可用
        """
        try:
            result = subprocess.run([self.ffmpeg_path, "-version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("FFmpeg检查成功")
                return True
            else:
                logger.error("FFmpeg检查失败")
                return False
        except FileNotFoundError:
            logger.error(f"找不到FFmpeg: {self.ffmpeg_path}")
            return False
        except subprocess.TimeoutExpired:
            logger.error("FFmpeg检查超时")
            return False
        except Exception as e:
            logger.error(f"FFmpeg检查异常: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Dict:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 文件信息字典
        """
        try:
            cmd = [
                self.ffmpeg_path, "-i", file_path,
                "-f", "null", "-"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            # 解析输出获取文件信息
            info = {
                "path": file_path,
                "exists": os.path.exists(file_path),
                "size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                "duration": None,
                "format": None,
                "error": None
            }
            
            if result.returncode == 0:
                # 成功获取信息
                output = result.stderr  # FFmpeg信息输出到stderr
                
                # 提取时长信息
                if "Duration:" in output:
                    duration_line = [line for line in output.split('\n') if "Duration:" in line][0]
                    duration_str = duration_line.split("Duration:")[1].split(",")[0].strip()
                    info["duration"] = duration_str
                
                # 提取格式信息
                if "Input #0" in output:
                    format_line = [line for line in output.split('\n') if "Input #0" in line][0]
                    info["format"] = format_line.split("Input #0,")[1].split(":")[0].strip()
                
            else:
                info["error"] = result.stderr
            
            return info
            
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_path}: {e}")
            return {
                "path": file_path,
                "exists": os.path.exists(file_path),
                "size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                "duration": None,
                "format": None,
                "error": str(e)
            }
    
    def merge_files_concat(self, input_files: List[str], output_file: str) -> Dict:
        """
        使用concat方法合并文件（适用于相同格式的文件）
        
        Args:
            input_files: 输入文件列表
            output_file: 输出文件路径
            
        Returns:
            Dict: 合并结果
        """
        try:
            # 检查输入文件
            valid_files = []
            for file_path in input_files:
                if os.path.exists(file_path):
                    valid_files.append(file_path)
                else:
                    logger.warning(f"文件不存在: {file_path}")
            
            if not valid_files:
                return {"success": False, "error": "没有有效的输入文件"}
            
            # 创建临时文件列表
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                temp_list_file = f.name
                for file_path in valid_files:
                    f.write(f"file '{file_path}'\n")
            
            try:
                # 执行FFmpeg命令
                cmd = [
                    self.ffmpeg_path,
                    "-f", "concat",
                    "-safe", "0",
                    "-i", temp_list_file,
                    "-c", "copy",
                    "-y",  # 覆盖输出文件
                    output_file
                ]
                
                logger.info(f"执行命令: {' '.join(cmd)}")
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    logger.info(f"文件合并成功: {output_file}")
                    return {
                        "success": True,
                        "output_file": output_file,
                        "input_files": valid_files,
                        "output_size": os.path.getsize(output_file) if os.path.exists(output_file) else 0
                    }
                else:
                    error_msg = result.stderr
                    logger.error(f"文件合并失败: {error_msg}")
                    return {
                        "success": False,
                        "error": error_msg,
                        "input_files": valid_files
                    }
                    
            finally:
                # 清理临时文件
                if os.path.exists(temp_list_file):
                    os.unlink(temp_list_file)
                    
        except Exception as e:
            logger.error(f"合并文件异常: {e}")
            return {"success": False, "error": str(e)}
    
    def merge_files_filter(self, input_files: List[str], output_file: str,
                          output_format: str = "mp4") -> Dict:
        """
        使用filter_complex方法合并文件（适用于不同格式的文件）
        
        Args:
            input_files: 输入文件列表
            output_file: 输出文件路径
            output_format: 输出格式
            
        Returns:
            Dict: 合并结果
        """
        try:
            # 检查输入文件
            valid_files = []
            for file_path in input_files:
                if os.path.exists(file_path):
                    valid_files.append(file_path)
                else:
                    logger.warning(f"文件不存在: {file_path}")
            
            if not valid_files:
                return {"success": False, "error": "没有有效的输入文件"}
            
            # 构建FFmpeg命令
            input_args = []
            filter_parts = []
            
            for i, file_path in enumerate(valid_files):
                input_args.extend(["-i", file_path])
                filter_parts.append(f"[{i}:a]")
            
            # 构建filter_complex
            filter_complex = f"{''.join(filter_parts)}concat=n={len(valid_files)}:v=0:a=1[outa]"
            
            cmd = [
                self.ffmpeg_path
            ] + input_args + [
                "-filter_complex", filter_complex,
                "-map", "[outa]",
                "-y",  # 覆盖输出文件
                output_file
            ]
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"文件合并成功: {output_file}")
                return {
                    "success": True,
                    "output_file": output_file,
                    "input_files": valid_files,
                    "output_size": os.path.getsize(output_file) if os.path.exists(output_file) else 0
                }
            else:
                error_msg = result.stderr
                logger.error(f"文件合并失败: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "input_files": valid_files
                }
                
        except Exception as e:
            logger.error(f"合并文件异常: {e}")
            return {"success": False, "error": str(e)}
    
    def merge_to_wav(self, input_files: List[str], output_file: str = "merged_audio.wav",
                    sample_rate: int = 16000, channels: int = 1) -> Dict:
        """
        将音频/视频文件合并为WAV格式
        
        Args:
            input_files: 输入文件列表（支持音频和视频文件）
            output_file: 输出WAV文件路径
            sample_rate: 采样率，默认44100Hz
            channels: 声道数，默认2（立体声）
            
        Returns:
            Dict: 合并结果
        """
        try:
            # 检查输入文件
            valid_files = []
            for file_path in input_files:
                if os.path.exists(file_path):
                    valid_files.append(file_path)
                else:
                    logger.warning(f"文件不存在: {file_path}")
            
            if not valid_files:
                return {"success": False, "error": "没有有效的输入文件"}
            
            # 构建FFmpeg命令
            input_args = []
            filter_parts = []
            
            for i, file_path in enumerate(valid_files):
                input_args.extend(["-i", file_path])
                filter_parts.append(f"[{i}:a]")
            
            # 构建filter_complex - 提取音频并合并
            filter_complex = f"{''.join(filter_parts)}concat=n={len(valid_files)}:v=0:a=1[outa]"
            
            cmd = [
                self.ffmpeg_path
            ] + input_args + [
                "-filter_complex", filter_complex,
                "-map", "[outa]",
                "-ar", str(sample_rate),  # 设置采样率
                "-ac", str(channels),     # 设置声道数
                "-acodec", "pcm_s16le",   # WAV格式编码
                "-y",  # 覆盖输出文件
                output_file
            ]
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"文件合并为WAV成功: {output_file}")
                return {
                    "success": True,
                    "output_file": output_file,
                    "input_files": valid_files,
                    "output_size": os.path.getsize(output_file) if os.path.exists(output_file) else 0,
                    "sample_rate": sample_rate,
                    "channels": channels
                }
            else:
                error_msg = result.stderr
                logger.error(f"文件合并为WAV失败: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "input_files": valid_files
                }
                
        except Exception as e:
            logger.error(f"合并文件为WAV异常: {e}")
            return {"success": False, "error": str(e)}
    
    def merge_files_auto(self, input_files: List[str], output_file: str) -> Dict:
        """
        自动选择合并方法
        
        Args:
            input_files: 输入文件列表
            output_file: 输出文件路径
            
        Returns:
            Dict: 合并结果
        """
        try:
            # 检查输入文件
            valid_files = []
            for file_path in input_files:
                if os.path.exists(file_path):
                    valid_files.append(file_path)
                else:
                    logger.warning(f"文件不存在: {file_path}")
            
            if not valid_files:
                return {"success": False, "error": "没有有效的输入文件"}
            
            # 检查文件格式是否相同
            file_extensions = [Path(f).suffix.lower() for f in valid_files]
            same_format = len(set(file_extensions)) == 1
            
            output_file_extension = Path(output_file).suffix.lower()

            if same_format:
                #替换output_file的扩展名
                output_file = output_file.replace(output_file_extension, file_extensions[0])
                logger.info("检测到相同格式文件，使用concat方法")
                return self.merge_files_concat(valid_files, output_file)
            else:
                logger.info("检测到不同格式文件，使用filter_complex方法")
                return self.merge_files_filter(valid_files, output_file)
                
        except Exception as e:
            logger.error(f"自动合并文件异常: {e}")
            return {"success": False, "error": str(e)}
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的格式列表
        
        Returns:
            List[str]: 支持的格式列表
        """
        return [
            # 音频格式
            "mp3", "wav", "aac", "ogg", "flac", "wma", "m4a",
            # 视频格式
            "mp4", "avi", "mov", "mkv", "wmv", "flv", "webm", "m4v"
        ]


def test_file_merger():
    """测试文件合并功能"""
    print("=== 文件合并工具测试 ===\n")
    
    # 创建合并器实例
    merger = FileMerger()
    
    # 检查FFmpeg
    if not merger._check_ffmpeg():
        print("❌ FFmpeg不可用，请先安装FFmpeg")
        return
    
    print("✅ FFmpeg检查通过\n")
    
    # 测试文件列表（请根据实际情况修改）
    test_files = [
        "1.mp4",  # 假设这是您的音频文件
        # "audio2.wav",
        # "audio3.mp3",
    ]
    
    print("📁 测试文件列表:")
    for i, file_path in enumerate(test_files, 1):
        info = merger.get_file_info(file_path)
        if info["exists"]:
            print(f"  {i}. {file_path} - {info['format']} - {info['duration']} - {info['size']} bytes")
        else:
            print(f"  {i}. {file_path} - ❌ 文件不存在")
    
    print()
    
    # 测试合并功能
    output_file = "merged_output.mp4"
    
    print(f"🔄 开始合并文件到: {output_file}")
    
    # 使用自动合并方法
    result = merger.merge_files_auto(test_files, output_file)
    
    if result["success"]:
        print(f"✅ 合并成功!")
        print(f"   输出文件: {result['output_file']}")
        print(f"   文件大小: {result['output_size']} bytes")
        print(f"   输入文件数: {len(result['input_files'])}")
    else:
        print(f"❌ 合并失败: {result['error']}")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    test_file_merger() 