<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阿里云OSS直传示例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .upload-section {
            margin-bottom: 40px;
        }

        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 12px;
            padding: 60px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: #e8f2ff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #999;
            font-size: 0.9em;
        }

        .file-input {
            display: none;
        }

        .progress-section {
            margin: 30px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            color: #666;
        }

        .result-section {
            margin-top: 30px;
            display: none;
        }

        .result-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
        }

        .result-item.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }

        .result-item h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .result-item p {
            margin: 5px 0;
            color: #666;
        }

        .result-item a {
            color: #667eea;
            text-decoration: none;
            word-break: break-all;
        }

        .result-item a:hover {
            text-decoration: underline;
        }

        .controls {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .api-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }

        .api-info h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .api-endpoint {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 5px 0;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 阿里云OSS直传</h1>
            <p>安全、快速的文件上传解决方案</p>
        </div>

        <div class="content">
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                    <div class="upload-hint">支持多文件上传，单个文件最大100MB</div>
                    <input type="file" id="fileInput" class="file-input" multiple>
                </div>
            </div>

            <div class="progress-section" id="progressSection">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备上传...</div>
            </div>

            <div class="result-section" id="resultSection">
                <h3>📋 上传结果</h3>
                <div id="resultList"></div>
            </div>

            <div class="controls">
                <button class="btn" id="uploadBtn" onclick="triggerFileSelect()">选择文件</button>
                <button class="btn btn-secondary" onclick="clearResults()">清除结果</button>
            </div>

            <div class="api-info">
                <h3>🔗 API 接口信息</h3>
                <p><strong>获取上传凭证:</strong></p>
                <div class="api-endpoint">GET /api/oss/upload-token</div>
                
                <p style="margin-top: 15px;"><strong>OSS配置信息:</strong></p>
                <div class="api-endpoint">GET /api/oss/config</div>
                
                <p style="margin-top: 15px;"><strong>当前API地址:</strong></p>
                <div class="api-endpoint" id="apiBaseUrl">http://localhost:8000</div>
            </div>
        </div>
    </div>

    <script>
        // 配置API基础URL - 请根据实际情况修改
        const API_BASE_URL = window.location.origin || 'http://localhost:8000';
        document.getElementById('apiBaseUrl').textContent = API_BASE_URL;

        // DOM元素
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const progressSection = document.getElementById('progressSection');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const resultSection = document.getElementById('resultSection');
        const resultList = document.getElementById('resultList');
        const uploadBtn = document.getElementById('uploadBtn');

        // 全局变量
        let isUploading = false;

        // 初始化事件监听器
        function initEventListeners() {
            // 点击上传区域
            uploadArea.addEventListener('click', triggerFileSelect);
            
            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);
            
            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            
            // 阻止页面默认拖拽行为
            document.addEventListener('dragover', e => e.preventDefault());
            document.addEventListener('drop', e => e.preventDefault());
        }

        // 触发文件选择
        function triggerFileSelect() {
            if (!isUploading) {
                fileInput.click();
            }
        }

        // 处理文件选择
        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            if (files.length > 0) {
                uploadFiles(files);
            }
        }

        // 处理拖拽悬停
        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        // 处理拖拽离开
        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        // 处理拖拽放下
        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files);
            if (files.length > 0) {
                uploadFiles(files);
            }
        }

        // 获取上传凭证
        async function getUploadToken(fileName = null) {
            try {
                const params = new URLSearchParams();
                if (fileName) {
                    params.append('file_name', fileName);
                }
                
                const response = await fetch(`${API_BASE_URL}/api/oss/upload-token?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `HTTP ${response.status}`);
                }
                
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('获取上传凭证失败:', error);
                throw error;
            }
        }

        // 上传到OSS
        async function uploadToOSS(file, token) {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                
                // 生成唯一文件名
                const timestamp = Date.now();
                const randomStr = Math.random().toString(36).substring(7);
                const fileExtension = file.name.split('.').pop();
                const uniqueFileName = `${timestamp}_${randomStr}.${fileExtension}`;
                const key = token.dir + uniqueFileName;
                
                // OSS表单参数 - 顺序很重要！
                formData.append('key', key);
                formData.append('policy', token.policy);
                formData.append('OSSAccessKeyId', token.access_key_id);
                formData.append('signature', token.signature);
                formData.append('x-oss-object-acl', 'public-read');
                formData.append('file', file);

                const xhr = new XMLHttpRequest();
                
                // 上传进度
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percent = Math.round((e.loaded / e.total) * 100);
                        updateProgress(percent, `上传中... ${percent}%`);
                    }
                });

                xhr.addEventListener('load', () => {
                    if (xhr.status === 204) {
                        // OSS上传成功返回204状态码
                        const fileUrl = `${token.host}/${key}`;
                        resolve({
                            success: true,
                            url: fileUrl,
                            key: key,
                            originalName: file.name,
                            size: file.size
                        });
                    } else {
                        reject(new Error(`上传失败: HTTP ${xhr.status}`));
                    }
                });

                xhr.addEventListener('error', () => {
                    reject(new Error('网络错误，请检查网络连接'));
                });

                xhr.addEventListener('timeout', () => {
                    reject(new Error('上传超时，请重试'));
                });

                // 设置超时时间（10分钟）
                xhr.timeout = 10 * 60 * 1000;
                
                xhr.open('POST', token.host);
                xhr.send(formData);
            });
        }

        // 更新进度
        function updateProgress(percent, text) {
            progressSection.style.display = 'block';
            progressFill.style.width = percent + '%';
            progressText.textContent = text;
        }

        // 隐藏进度条
        function hideProgress() {
            setTimeout(() => {
                progressSection.style.display = 'none';
                progressFill.style.width = '0%';
            }, 2000);
        }

        // 添加结果到列表
        function addResult(result, isError = false) {
            resultSection.style.display = 'block';
            
            const resultItem = document.createElement('div');
            resultItem.className = `result-item${isError ? ' error' : ''}`;
            
            if (isError) {
                resultItem.innerHTML = `
                    <h3>❌ 上传失败</h3>
                    <p><strong>错误信息:</strong> ${result}</p>
                `;
            } else {
                const sizeText = formatFileSize(result.size);
                resultItem.innerHTML = `
                    <h3>✅ 上传成功</h3>
                    <p><strong>文件名:</strong> ${result.originalName}</p>
                    <p><strong>文件大小:</strong> ${sizeText}</p>
                    <p><strong>访问链接:</strong> <a href="${result.url}" target="_blank">${result.url}</a></p>
                    <p><strong>OSS Key:</strong> ${result.key}</p>
                `;
            }
            
            resultList.appendChild(resultItem);
            
            // 滚动到结果区域
            resultItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 设置上传状态
        function setUploadingState(uploading) {
            isUploading = uploading;
            uploadBtn.disabled = uploading;
            uploadArea.style.pointerEvents = uploading ? 'none' : 'auto';
            uploadArea.style.opacity = uploading ? '0.6' : '1';
        }

        // 上传文件
        async function uploadFiles(files) {
            if (isUploading) return;
            
            setUploadingState(true);
            
            try {
                updateProgress(0, '准备上传...');
                
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    
                    try {
                        updateProgress(0, `正在上传第 ${i + 1}/${files.length} 个文件: ${file.name}`);
                        
                        // 获取上传凭证
                        const token = await getUploadToken(file.name);
                        
                        // 上传到OSS
                        const result = await uploadToOSS(file, token);
                        
                        // 添加成功结果
                        addResult(result);
                        
                        updateProgress(100, `第 ${i + 1}/${files.length} 个文件上传完成`);
                        
                    } catch (error) {
                        console.error(`文件 ${file.name} 上传失败:`, error);
                        addResult(`文件 "${file.name}" 上传失败: ${error.message}`, true);
                    }
                }
                
                updateProgress(100, `所有文件上传完成！`);
                hideProgress();
                
            } catch (error) {
                console.error('上传过程出错:', error);
                addResult(`上传过程出错: ${error.message}`, true);
                hideProgress();
            } finally {
                setUploadingState(false);
                // 清空文件选择
                fileInput.value = '';
            }
        }

        // 清除结果
        function clearResults() {
            resultSection.style.display = 'none';
            resultList.innerHTML = '';
            progressSection.style.display = 'none';
            progressFill.style.width = '0%';
            fileInput.value = '';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initEventListeners);
    </script>
</body>
</html> 