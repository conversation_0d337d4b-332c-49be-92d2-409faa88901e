@echo off
echo 启动开发环境...
echo.

REM 启动 Redis 和 MySQL 服务
echo 正在启动 Redis 和 MySQL 服务...
docker-compose -f docker-compose.dev.yml up -d

echo.
echo 等待服务启动...
timeout /t 5 /nobreak > nul

echo.
echo 开发环境启动完成！
echo.
echo 服务地址：
echo - Redis: localhost:6379
echo - MySQL: localhost:3308
echo - FastAPI: http://localhost:8000
echo.
echo 接下来可以：
echo 1. 运行 start_celery_worker.bat 启动 Celery Worker
echo 2. 运行 start_celery_beat.bat 启动 Celery Beat 调度器（可选）
echo 3. 运行 run-dev.bat 启动 FastAPI 应用
echo.

pause 