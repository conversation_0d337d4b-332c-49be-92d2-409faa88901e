# FunASR Celery 集成使用说明

## 概述

本项目已将 FunASR 语音识别模型集成到 Celery 任务系统中，支持在 Celery Worker 启动时预加载模型，提高音频转写任务的执行效率。

## 主要特性

- ✅ **Worker 启动时模型预加载**: 避免每次任务执行时重新初始化模型
- ✅ **懒加载机制**: 如果预加载失败，会在首次使用时自动初始化
- ✅ **Celery 任务集成**: 提供标准的 Celery 任务接口
- ✅ **任务状态跟踪**: 实时更新任务执行状态和进度
- ✅ **错误处理**: 完善的异常处理和错误恢复机制

## 系统要求

1. **Python 3.11+**
2. **Redis 服务器** - 作为 Celery 的消息代理
3. **CUDA 支持** - 用于 GPU 加速（可选，CPU 模式也可运行）
4. **FunASR 依赖**:
   - funasr>=1.2.6
   - torch>=1.9.0
   - torchaudio>=0.9.0

## 安装和配置

### 1. 安装依赖

```bash
# 使用 uv 安装依赖
uv sync

# 或者使用 pip
pip install -r requirements.txt
```

### 2. 配置 FunASR

在 `conf/develop.yaml` 中配置 FunASR 参数：

```yaml
FUNASR:
  BATCH_SIZE_S: 300
  MODEL: 'paraformer-zh'
  VAD_MODEL: 'fsmn-vad'
  PUNC_MODEL: 'ct-punc'
  SPK_MODEL: 'cam++'
```

### 3. 启动 Redis 服务

确保 Redis 服务器正在运行：

```bash
# Windows (使用 Docker)
docker run -d -p 6379:6379 redis:alpine

# Linux/Mac
redis-server
```

## 使用方法

### 1. 启动 Celery Worker

#### 方法一：使用批处理脚本（Windows）

```bash
# 启动带有 FunASR 模型预加载的 Worker
start_funasr_worker.bat
```

#### 方法二：命令行启动

```bash
# 启动 Celery Worker
celery -A app.tasks.celery_app worker --loglevel=info --pool=solo --concurrency=1
```

**注意**: Worker 启动时会自动初始化 FunASR 模型，这可能需要几分钟时间。

### 2. 提交音频转写任务

#### 方法一：使用 Celery 任务

```python
from app.tasks.funasr import transcribe_audio_task

# 提交音频转写任务
task = transcribe_audio_task.delay("path/to/audio.wav")

# 等待任务完成
result = task.get(timeout=300)  # 5分钟超时

# 查看任务状态
print(f"任务状态: {task.status}")
print(f"转写结果: {result}")
```

#### 方法二：直接调用函数

```python
from app.tasks.funasr import process_single_file

# 直接处理音频文件
result = process_single_file("path/to/audio.wav")
print(f"转写结果: {result}")
```

### 3. 监控任务状态

```python
from app.tasks.funasr import transcribe_audio_task

# 提交任务
task = transcribe_audio_task.delay("audio.wav")

# 检查任务状态
while not task.ready():
    print(f"任务状态: {task.status}")
    print(f"任务信息: {task.info}")
    time.sleep(5)

# 获取最终结果
result = task.get()
```

## 任务结果格式

音频转写任务返回的结果格式如下：

```python
{
    "file_path": "path/to/audio.wav",
    "file_name": "audio.wav",
    "file_size_mb": 15.5,
    "speaker_segments": [
        {
            "start_time": 0.0,
            "end_time": 2.5,
            "speaker": "0",
            "text": "你好，欢迎参加会议"
        },
        {
            "start_time": 2.5,
            "end_time": 5.0,
            "speaker": "1",
            "text": "谢谢，很高兴参加"
        }
    ],
    "statistics": {
        "transcription_time": 12.5
    }
}
```

## 性能优化

### 1. 模型预加载

Worker 启动时自动预加载模型，避免每次任务执行时的初始化开销：

```python
# 在 app/tasks/__init__.py 中配置
@celery_app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """Worker 启动时的配置函数"""
    from app.tasks.funasr import init_model
    init_model()  # 预加载模型
```

### 2. 懒加载机制

如果预加载失败，会在首次使用时自动初始化：

```python
def get_or_init_model():
    """获取或初始化模型"""
    global funasr_model
    global is_init_model
    
    if funasr_model is None or not is_init_model:
        funasr_model = init_model()
    
    return funasr_model
```

### 3. 并发配置

根据服务器性能调整并发数：

```bash
# 单进程模式（推荐用于 GPU 模型）
celery -A app.tasks.celery_app worker --pool=solo --concurrency=1

# 多进程模式（CPU 模式）
celery -A app.tasks.celery_app worker --pool=prefork --concurrency=4
```

## 测试

### 运行测试脚本

```bash
# 运行 FunASR Celery 测试
python test_funasr_celery.py
```

### 测试内容

1. **模型初始化测试**: 验证模型能否正常加载
2. **Celery 任务测试**: 验证任务提交和执行
3. **音频转写测试**: 验证完整的音频转写流程

## 故障排除

### 常见问题

1. **模型初始化失败**

   **症状**: Worker 启动时显示模型初始化失败
   
   **解决方案**:
   - 检查 CUDA 环境是否正确安装
   - 验证 FunASR 依赖是否完整
   - 尝试使用 CPU 模式：修改 `device="cpu"`

2. **内存不足**

   **症状**: 模型加载时出现内存错误
   
   **解决方案**:
   - 减少 `BATCH_SIZE_S` 参数
   - 使用更小的模型
   - 增加系统内存

3. **任务执行超时**

   **症状**: 音频转写任务超时
   
   **解决方案**:
   - 增加 `TASK_TIME_LIMIT` 配置
   - 检查音频文件大小和格式
   - 优化模型参数

4. **Redis 连接失败**

   **症状**: Celery 无法连接到 Redis
   
   **解决方案**:
   - 检查 Redis 服务是否启动
   - 验证 Redis 连接配置
   - 检查防火墙设置

### 日志分析

查看 Celery Worker 日志：

```bash
# 启动 Worker 时查看详细日志
celery -A app.tasks.celery_app worker --loglevel=debug
```

### 性能监控

使用 Celery 监控工具：

```bash
# 查看任务统计
celery -A app.tasks.celery_app inspect stats

# 查看活跃任务
celery -A app.tasks.celery_app inspect active

# 查看任务历史
celery -A app.tasks.celery_app inspect reserved
```

## 高级配置

### 1. 自定义模型配置

修改 `app/tasks/funasr.py` 中的模型初始化参数：

```python
model = AutoModel(
    model=cfg.FUNASR.MODEL,
    vad_model=cfg.FUNASR.VAD_MODEL,
    punc_model=cfg.FUNASR.PUNC_MODEL,
    spk_model=cfg.FUNASR.SPK_MODEL,
    device="cuda",  # 或 "cpu"
    disable_update=True,
    return_raw_text=False,
    return_spk_res=True,
    sentence_timestamp=False,
)
```

### 2. 添加热词支持

```python
# 在配置文件中添加热词
FUNASR:
  HOTWORDS: ["专业术语1", "专业术语2"]

# 在模型调用时使用热词
results = model.generate(
    input=audio_file,
    batch_size_s=cfg.FUNASR.BATCH_SIZE_S,
    hotword=cfg.FUNASR.HOTWORDS
)
```

### 3. 批量处理优化

```python
# 批量处理多个音频文件
def process_batch_files(audio_files):
    """批量处理音频文件"""
    results = []
    for audio_file in audio_files:
        task = transcribe_audio_task.delay(audio_file)
        results.append(task)
    return results
```

## 部署建议

### 1. 生产环境配置

```bash
# 使用 supervisor 管理 Celery Worker
[program:celery_worker]
command=/path/to/venv/bin/celery -A app.tasks.celery_app worker --loglevel=info --pool=solo
directory=/path/to/project
user=celery
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/celery/worker.log
```

### 2. 监控和告警

```python
# 添加任务失败监控
@celery_app.task(bind=True)
def monitored_transcribe_task(self, audio_file):
    try:
        return transcribe_audio_task(audio_file)
    except Exception as e:
        # 发送告警通知
        send_alert(f"音频转写任务失败: {e}")
        raise
```

### 3. 资源管理

- **GPU 内存**: 监控 GPU 内存使用情况
- **CPU 使用率**: 根据 CPU 核心数调整并发数
- **磁盘空间**: 定期清理临时音频文件

## 总结

通过将 FunASR 模型集成到 Celery 任务系统中，我们实现了：

1. **高效的模型管理**: Worker 启动时预加载，避免重复初始化
2. **可扩展的架构**: 支持多 Worker 并发处理
3. **可靠的错误处理**: 完善的异常处理和恢复机制
4. **灵活的配置**: 支持多种部署模式和参数调整

这种架构特别适合需要处理大量音频转写任务的场景，能够显著提高系统的整体性能和可靠性。 