# routers/messages.py
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

from app.models import  Conversation, Message, HttpResult, success
from app.utils import get_current_user
import logging
from config import get_db

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api", tags=["messages"])

# 模型定义
class MessageResponse(BaseModel):
    id: int
    role: str
    content: str
    reasoning_content: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class ConversationInfo(BaseModel):
    id: int
    title: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class MessagesResponse(BaseModel):
    messages: List[MessageResponse]
    total: int
    conversation: ConversationInfo

class DeleteResponse(BaseModel):
    success: bool
    message: str

# 获取对话消息 
@router.get("/conversations/messages/{conv_id}", response_model=HttpResult[MessagesResponse])
async def get_messages(
    conv_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
      获取特定会话的所有消息
    '''
    try:
        # 验证对话存在且属于当前用户
        conv = db.query(Conversation).filter(
            Conversation.id == conv_id,
            Conversation.user_id == current_user_id
        ).first()

        if not conv:
            raise HTTPException(status_code=404, detail="对话不存在或无权访问")

        # 查询所有消息，按时间升序排列
        messages_query = db.query(Message).filter(
            Message.conversation_id == conv_id
        ).order_by(Message.created_at.asc())
        
        # 获取所有消息
        messages = messages_query.all()
        
        # 计算总数
        total = len(messages)

        # 准备响应数据
        result = []
        for msg in messages:
            result.append(MessageResponse(
                id=msg.id,
                role=msg.role,
                content=msg.content,
                reasoning_content=msg.reasoning_content,
                created_at=msg.created_at.isoformat() if msg.created_at else None,
                updated_at=msg.updated_at.isoformat() if msg.updated_at else None
            ))
        
        # 更新对话的最后访问时间
        if hasattr(conv, 'last_accessed_at'):
            conv.last_accessed_at = datetime.utcnow()
            db.commit()
        
        return success(MessagesResponse(
            messages=result,
            total=total,
            conversation=ConversationInfo(
                id=conv.id,
                title=conv.title,
                created_at=conv.created_at.isoformat() if conv.created_at else None,
                updated_at=conv.updated_at.isoformat() if conv.updated_at else None
            )
        ))
        
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"数据库错误: {str(e)}")
        raise HTTPException(status_code=500, detail="数据库查询错误")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取消息时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
