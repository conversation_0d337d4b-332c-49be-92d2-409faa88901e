services:
  funasr-executor:
    image: funasr-worker:0.0.2
    container_name: funasr-executor
    restart: always
    networks:
      - meeting_network
    environment:
      - MODELSCOPE_CACHE=/workspace/models
      - MEETING_CONF_CELERY__BROKER_URL=redis://************:6379/0
      - MEETING_CONF_CELERY__RESULT_BACKEND=redis://************:6379/1
      - TZ=Asia/Shanghai
      - PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True

    volumes:
      - D:/cache_data:/workspace/models

    command:
      - python
      - scripts/start_executor_worker.py
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: [ "0" ]
              capabilities: [ gpu ]

networks:
  meeting_network:
    driver: bridge
