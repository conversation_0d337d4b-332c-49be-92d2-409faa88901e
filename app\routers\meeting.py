# routers/meeting.py
import json
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime, timedelta
import requests

from app.models import Meeting, HttpResult, success, error
from app.utils import get_current_user, safe_format_result_for_storage
import logging
from app.mysql import get_db

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api/meeting", tags=["会议管理"])

# 分页响应模型
class MeetingResponse(BaseModel):
    id: int
    name: str
    users: Optional[str] = None
    meetType: Optional[str] = None
    desc: Optional[str] = None
    audioOriginSize: Optional[int] = None
    audioDuration: Optional[int] = None
    audioUrl: Optional[str] = None
    audioSize: Optional[int] = None
    audioState: str

    created_at: Optional[str] = None
    updated_at: Optional[str] = None

# 会议详情响应模型
class MeetingDetailResponse(BaseModel):
    id: int
    name: str
    users: Optional[str] = None
    meetType: Optional[str] = None
    desc: Optional[str] = None
    user_id: int
    audioOriginUrls: str
    audioOriginSize: Optional[int] = None
    audioDuration: Optional[int] = None
    audioUrl: Optional[str] = None
    audioSize: Optional[int] = None
    audioState: str
    celeryTaskId: Optional[str] = None
    celeryTaskStatus: Optional[str] = None
    celeryTaskResult: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class PaginatedMeetingResponse(BaseModel):
    meetings: List[MeetingResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

# 创建会议请求模型
class CreateMeetingRequest(BaseModel):
    name: str
    meetType: Optional[str] = None
    users: Optional[str] = None
    desc: Optional[str] = None
    audioOriginUrls: str
    audioOriginSize: Optional[int] = None
    audioDuration: Optional[int] = None
    audioUrl: Optional[str] = None
    audioSize: Optional[int] = None
    audioState: str = "uploaded"  # 默认状态

# 创建会议响应模型
class CreateMeetingResponse(BaseModel):
    id: int
    name: str
    meetType: Optional[str] = None
    users: Optional[str] = None
    desc: Optional[str] = None
    created_at: str
    updated_at: str
    audioState: str

# 音频转换状态响应模型
class AudioConversionStatusResponse(BaseModel):
    meeting_id: int
    audio_state: str
    task_status: Optional[str] = None
    progress: Optional[int] = None
    current_file: Optional[int] = None
    total_files: Optional[int] = None
    error_message: Optional[str] = None
    celery_task_id: Optional[str] = None
    celery_task_status: Optional[str] = None
    celery_task_result: Optional[str] = None
    estimated_completion_time: Optional[str] = None

# 获取所有会议列表
@router.get("/list", response_model=HttpResult[PaginatedMeetingResponse])
async def get_meetings(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量，最大100"),
    keywords: Optional[str] = Query(None, description="搜索关键字模糊匹配"),
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    获取所有会议列表（支持分页）
    '''
    try:
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询总数
        total_count = db.query(func.count(Meeting.id))\
            .filter(Meeting.user_id == current_user_id)\
            .scalar()
        
        # 查询会议列表（分页）
        meetings = db.query(
            Meeting.id,
            Meeting.name,
            Meeting.users,
            Meeting.meetType,
            Meeting.desc,
            Meeting.createdAt,
            Meeting.updatedAt,
            Meeting.audioState,
            Meeting.audioOriginSize,
            Meeting.audioDuration,
            Meeting.audioUrl,
            Meeting.audioSize
        ).filter(Meeting.user_id == current_user_id)\
         .order_by(Meeting.createdAt.desc())\
         .offset(offset)\
         .limit(page_size)\
         .all()

        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size

        # 格式化响应数据
        meeting_list = []
        for meeting in meetings:
            meeting_list.append(MeetingResponse(
                id=meeting.id,
                name=meeting.name,
                users=meeting.users,
                meetType=meeting.meetType,
                desc=meeting.desc,
                audioOriginSize=meeting.audioOriginSize,
                audioDuration=meeting.audioDuration,
                audioUrl=meeting.audioUrl,
                audioSize=meeting.audioSize,
                created_at=meeting.createdAt.isoformat() if meeting.createdAt else None,
                updated_at=meeting.updatedAt.isoformat() if meeting.updatedAt else None,
                audioState=meeting.audioState
            ))

        return success(PaginatedMeetingResponse(
            meetings=meeting_list,
            total=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))

    except Exception as e:
        logger.error(f"获取会议列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取会议列表失败")

# 获取会议详情
@router.get("/detail/{meeting_id}", response_model=HttpResult[MeetingDetailResponse])
async def get_meeting_detail(
    meeting_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    获取会议详情
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 构造响应数据
        meeting_detail = MeetingDetailResponse(
            id=meeting.id,
            name=meeting.name,
            users=meeting.users,
            meetType=meeting.meetType,
            desc=meeting.desc,
            user_id=meeting.user_id,
            audioOriginUrls=meeting.audioOriginUrls,
            audioOriginSize=meeting.audioOriginSize,
            audioDuration=meeting.audioDuration,
            audioUrl=meeting.audioUrl,
            audioSize=meeting.audioSize,
            audioState=meeting.audioState,
            celeryTaskId=meeting.celeryTaskId,
            celeryTaskStatus=meeting.celeryTaskStatus,
            celeryTaskResult=meeting.celeryTaskResult,
            created_at=meeting.createdAt.isoformat() if meeting.createdAt else None,
            updated_at=meeting.updatedAt.isoformat() if meeting.updatedAt else None
        )

        return success(meeting_detail)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会议详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取会议详情失败")

# 创建新会议
@router.post("/create", response_model=HttpResult[CreateMeetingResponse])
async def create_meeting(
    meeting_data: CreateMeetingRequest,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    创建新会议
    '''
    try:
        # 验证必填字段
        if not meeting_data.name or len(meeting_data.name.strip()) == 0:
            raise HTTPException(status_code=400, detail="会议名称不能为空")
        
        if not meeting_data.audioOriginUrls or len(meeting_data.audioOriginUrls.strip()) == 0:
            raise HTTPException(status_code=400, detail="音频原始URL不能为空")
        
        #校验audioOriginUrls是一个数组字符串能被json.loads解析
        try:
            audioOriginUrls = json.loads(meeting_data.audioOriginUrls)
            if not isinstance(audioOriginUrls, list) and len(audioOriginUrls) < 1:
                raise HTTPException(status_code=400, detail="音频原始URL格式错误")
            if len(audioOriginUrls) == 1:
                meeting_data.audioUrl = audioOriginUrls[0]
                meeting_data.audioSize = meeting_data.audioOriginSize

        except:
            raise HTTPException(status_code=400, detail="音频原始URL格式错误")
        
        # 验证音频状态
        valid_states = ["uploaded", "handing", "finished", "canceled", "failed"]
        if meeting_data.audioState not in valid_states:
            raise HTTPException(status_code=400, detail=f"音频状态必须是以下之一: {', '.join(valid_states)}")
        
        # 创建新会议
        new_meeting = Meeting(
            name=meeting_data.name.strip(),
            meetType=meeting_data.meetType,
            users=meeting_data.users,
            desc=meeting_data.desc,
            user_id=current_user_id,
            audioOriginUrls=meeting_data.audioOriginUrls,
            audioOriginSize=meeting_data.audioOriginSize,
            audioDuration=meeting_data.audioDuration,
            audioUrl=meeting_data.audioUrl,
            audioSize=meeting_data.audioSize,
            audioState=meeting_data.audioState,
        )
        
        db.add(new_meeting)
        db.commit()
        db.refresh(new_meeting)

        #调用音频转换celery任务
        from app.tasks.executor import transcribe_audio_task
        
        try:
            # 提交任务
            audioOriginUrls = json.loads(new_meeting.audioOriginUrls)
            task = transcribe_audio_task.delay(new_meeting.id, audioOriginUrls)
            
            if not task or not task.id:
                # 任务提交失败
                logger.error(f"Celery任务提交失败，会议ID: {new_meeting.id}")
                # 回滚数据库事务
                db.rollback()
                raise HTTPException(status_code=500, detail="音频转换任务提交失败，请稍后重试")

            print(f"任务已提交，ID: {task.id}")

            # 更新会议信息
            new_meeting.celeryTaskId = task.id
            new_meeting.celeryTaskStatus = "pending"

            try:
                db.commit()
                db.refresh(new_meeting)
            except Exception as db_error:
                # 数据库更新失败，但任务已经提交
                logger.error(f"数据库更新失败，但任务已提交，会议ID: {new_meeting.id}, 任务ID: {task.id}, 错误: {str(db_error)}")
                # 尝试取消已提交的任务
                try:
                    task.revoke(terminate=True)
                    logger.info(f"已取消任务: {task.id}")
                except Exception as revoke_error:
                    logger.warning(f"取消任务失败: {task.id}, 错误: {str(revoke_error)}")
                
                raise HTTPException(status_code=500, detail="会议创建成功但状态更新失败，请稍后重试")
                
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as task_error:
            # 任务提交过程中的其他异常
            logger.error(f"音频转换任务提交异常，会议ID: {new_meeting.id}, 错误: {str(task_error)}")
            # 回滚数据库事务
            db.rollback()
            raise HTTPException(status_code=500, detail="音频转换任务提交失败，请稍后重试")
            
        return success(CreateMeetingResponse(
            id=new_meeting.id,
            name=new_meeting.name,
            meetType=new_meeting.meetType,
            users=new_meeting.users,
            desc=new_meeting.desc,
            created_at=new_meeting.createdAt.isoformat() if new_meeting.createdAt else None,
            updated_at=new_meeting.updatedAt.isoformat() if new_meeting.updatedAt else None,
            audioState=new_meeting.audioState
        ))

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建会议失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 查询音频转换状态
@router.get("/audio-status/{meeting_id}", response_model=HttpResult[AudioConversionStatusResponse])
async def get_audio_conversion_status(
    meeting_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    查询音频转换任务状态
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 获取Celery任务状态
        task_status = None
        progress = None
        current_file = None
        total_files = None
        error_message = None
        estimated_completion_time = None
        
        if meeting.celeryTaskId:
            try:
                from app.tasks import celery_app
                
                # 查询Celery任务状态
                task_result = celery_app.AsyncResult(meeting.celeryTaskId)
                
                if task_result.state == 'PENDING':
                    task_status = "pending"
                elif task_result.state == 'STARTED':
                    task_status = "running"
                elif task_result.state == 'PROGRESS':
                    task_status = "progress"
                    # 获取进度信息
                    if task_result.info:
                        progress = task_result.info.get('progress')
                        current_file = task_result.info.get('current')
                        total_files = task_result.info.get('total')
                elif task_result.state == 'SUCCESS':
                    task_status = "success"
                elif task_result.state == 'FAILURE':
                    task_status = "failure"
                    if task_result.info:
                        error_message = str(task_result.info)
                elif task_result.state == 'REVOKED':
                    task_status = "canceled"
                
                # 估算完成时间（如果任务正在运行）
                if task_status in ["running", "progress"] and meeting.createdAt:
                    # 简单的估算：基于已处理时间
                    elapsed_time = (datetime.utcnow() - meeting.createdAt).total_seconds()
                    if progress and progress > 0:
                        estimated_total_time = elapsed_time / (progress / 100)
                        remaining_time = estimated_total_time - elapsed_time
                        if remaining_time > 0:
                            estimated_completion_time = (datetime.utcnow() + timedelta(seconds=remaining_time)).isoformat()
                
            except Exception as e:
                logger.warning(f"查询Celery任务状态失败: {str(e)}")
                # 如果无法查询Celery状态，使用数据库中的状态
                task_status = meeting.celeryTaskStatus
        
        return success(AudioConversionStatusResponse(
            meeting_id=meeting.id,
            audio_state=meeting.audioState,
            task_status=task_status,
            progress=progress,
            current_file=current_file,
            total_files=total_files,
            error_message=error_message,
            celery_task_id=meeting.celeryTaskId,
            celery_task_status=meeting.celeryTaskStatus,
            celery_task_result=meeting.celeryTaskResult,
            estimated_completion_time=estimated_completion_time
        ))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询音频转换状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="查询音频转换状态失败")

# 删除会议
@router.post("/delete/{meeting_id}", response_model=HttpResult[str])
async def delete_meeting(
    meeting_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    删除会议
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 删除会议
        db.delete(meeting)
        db.commit()
        
        return success("会议已删除")
        
    except HTTPException:

        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除会议时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="删除会议失败")

# 更新会议请求模型
class UpdateMeetingRequest(BaseModel):
    name: Optional[str] = None
    meetType: Optional[str] = None
    users: Optional[str] = None
    desc: Optional[str] = None
    audioOriginUrls: Optional[str] = None
    audioOriginSize: Optional[int] = None
    audioDuration: Optional[int] = None
    audioUrl: Optional[str] = None
    audioSize: Optional[int] = None
    audioState: Optional[str] = None

# 更新会议响应模型
class UpdateMeetingResponse(BaseModel):
    id: int
    name: str
    meetType: Optional[str] = None
    users: Optional[str] = None
    desc: Optional[str] = None
    audioOriginUrls: str
    audioOriginSize: Optional[int] = None
    audioDuration: Optional[int] = None
    audioUrl: Optional[str] = None
    audioSize: Optional[int] = None
    audioState: str
    created_at: str
    updated_at: str

# 更新会议
@router.post("/update/{meeting_id}", response_model=HttpResult[UpdateMeetingResponse])
async def update_meeting(
    meeting_id: int,
    meeting_data: UpdateMeetingRequest,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    更新会议信息
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 验证音频状态（如果提供）
        if meeting_data.audioState is not None:
            valid_states = ["uploaded", "handing", "finished", "canceled", "failed"]
            if meeting_data.audioState not in valid_states:
                raise HTTPException(status_code=400, detail=f"音频状态必须是以下之一: {', '.join(valid_states)}")
        
        # 更新会议信息
        if meeting_data.name is not None:
            if len(meeting_data.name.strip()) == 0:
                raise HTTPException(status_code=400, detail="会议名称不能为空")
            meeting.name = meeting_data.name.strip()
        
        if meeting_data.meetType is not None:
            meeting.meetType = meeting_data.meetType
        
        if meeting_data.users is not None:
            meeting.users = meeting_data.users
        
        if meeting_data.desc is not None:
            meeting.desc = meeting_data.desc
        
        if meeting_data.audioOriginUrls is not None:
            if len(meeting_data.audioOriginUrls.strip()) == 0:
                raise HTTPException(status_code=400, detail="音频原始URL不能为空")
            meeting.audioOriginUrls = meeting_data.audioOriginUrls.strip()
        
        if meeting_data.audioOriginSize is not None:
            meeting.audioOriginSize = meeting_data.audioOriginSize
        
        if meeting_data.audioDuration is not None:
            meeting.audioDuration = meeting_data.audioDuration
        
        if meeting_data.audioUrl is not None:
            meeting.audioUrl = meeting_data.audioUrl
        
        if meeting_data.audioSize is not None:
            meeting.audioSize = meeting_data.audioSize
        
        if meeting_data.audioState is not None:
            meeting.audioState = meeting_data.audioState
        
        # 更新修改时间
        meeting.updatedAt = datetime.utcnow()
        
        db.commit()
        db.refresh(meeting)

        return success(UpdateMeetingResponse(
            id=meeting.id,
            name=meeting.name,
            meetType=meeting.meetType,
            users=meeting.users,
            desc=meeting.desc,
            audioOriginUrls=meeting.audioOriginUrls,
            audioOriginSize=meeting.audioOriginSize,
            audioDuration=meeting.audioDuration,
            audioUrl=meeting.audioUrl,
            audioSize=meeting.audioSize,
            audioState=meeting.audioState,
            created_at=meeting.createdAt.isoformat() if meeting.createdAt else None,
            updated_at=meeting.updatedAt.isoformat() if meeting.updatedAt else None
        ))

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新会议时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="更新会议失败")

# 任务重试请求模型
class RetryTaskRequest(BaseModel):
    force: bool = False  # 是否强制重试

# 任务取消请求模型
class CancelTaskRequest(BaseModel):
    reason: Optional[str] = None

# 重试失败的任务
@router.post("/retry-task/{meeting_id}", response_model=HttpResult[str])
async def retry_failed_task(
    meeting_id: int,
    retry_data: RetryTaskRequest,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    重试失败的任务
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 检查任务状态
        # if meeting.audioState not in ["failed", "canceled"]:
        #     if not retry_data.force:
        #         raise HTTPException(status_code=400, detail="只有失败或已取消的任务可以重试")
        
        # 检查是否有正在运行的任务
        if meeting.audioState == "handing" and meeting.celeryTaskId:
            try:
                from app.tasks import celery_app
                task_result = celery_app.AsyncResult(meeting.celeryTaskId)
                print("检查任务状态", task_result.state)
                
                if task_result.state in ['PENDING', 'STARTED', 'PROGRESS']:
                    if not retry_data.force:
                        raise HTTPException(status_code=400, detail="任务正在运行中，无法重试")
                    else:
                        # 强制取消当前任务
                        task_result.revoke(terminate=True)
                        logger.info(f"强制取消任务: {meeting.celeryTaskId}")
            except Exception as e:
                logger.warning(f"检查任务状态失败: {str(e)}")
        
        # 重新提交任务
        try:
            from app.tasks.executor import transcribe_audio_task
            
            # 解析音频原始URL
            audio_origin_urls = json.loads(meeting.audioOriginUrls)
            
            # 提交新任务
            task = transcribe_audio_task.delay(meeting.id, audio_origin_urls)
            
            if not task or not task.id:
                raise HTTPException(status_code=500, detail="任务提交失败")
            
            # 更新会议信息
            meeting.celeryTaskId = task.id
            meeting.celeryTaskStatus = "pending"
            meeting.audioState = "handing"
            meeting.updatedAt = datetime.utcnow()
            
            db.commit()
            
            logger.info(f"会议 {meeting_id} 任务重试成功，新任务ID: {task.id}")
            
            return success(f"任务重试成功，新任务ID: {task.id}")
            
        except Exception as e:
            logger.error(f"重试任务失败: {str(e)}")
            raise HTTPException(status_code=500, detail="任务重试失败")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"重试任务时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="重试任务失败")

# 取消正在运行的任务
@router.post("/cancel-task/{meeting_id}", response_model=HttpResult[str])
async def cancel_running_task(
    meeting_id: int,
    cancel_data: CancelTaskRequest,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    取消正在运行的任务
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 检查任务状态
        if meeting.audioState not in ["handing", "uploaded"]:
            raise HTTPException(status_code=400, detail="只有正在处理或已上传的任务可以取消")
        
        # 取消Celery任务
        if meeting.celeryTaskId:
            try:
                from app.tasks import celery_app
                task_result = celery_app.AsyncResult(meeting.celeryTaskId)
                
                if task_result.state in ['PENDING', 'STARTED', 'PROGRESS']:
                    # 取消任务
                    task_result.revoke(terminate=True)
                    logger.info(f"取消任务: {meeting.celeryTaskId}")
                else:
                    logger.warning(f"任务状态为 {task_result.state}，无法取消")
                    
            except Exception as e:
                logger.warning(f"取消Celery任务失败: {str(e)}")
        
        # 更新会议状态
        meeting.audioState = "canceled"
        meeting.celeryTaskStatus = "canceled"
        meeting.celeryTaskResult = safe_format_result_for_storage({
            "canceled_at": datetime.utcnow().isoformat(),
            "reason": cancel_data.reason or "用户手动取消"
        })
        meeting.updatedAt = datetime.utcnow()
        
        db.commit()
        
        logger.info(f"会议 {meeting_id} 任务已取消")
        
        return success("任务已取消")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"取消任务时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="取消任务失败")

# 获取任务历史
@router.get("/task-history/{meeting_id}", response_model=HttpResult[List[dict]])
async def get_task_history(
    meeting_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    获取任务执行历史
    '''
    try:
        # 验证会议存在且属于当前用户
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()

        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在或无权访问")
        
        # 构建任务历史记录
        task_history = []
        
        if meeting.celeryTaskId:
            task_history.append({
                "task_id": meeting.celeryTaskId,
                "task_type": "transcribe_audio",
                "status": meeting.celeryTaskStatus,
                "audio_state": meeting.audioState,
                "created_at": meeting.createdAt.isoformat() if meeting.createdAt else None,
                "updated_at": meeting.updatedAt.isoformat() if meeting.updatedAt else None,
                "result": meeting.celeryTaskResult
            })
        
        return success(task_history)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取任务历史失败")

# 任务监控器状态响应模型
class MonitorStatusResponse(BaseModel):
    running: bool
    poll_interval: int
    total_checks: int
    total_updates: int

# 获取任务监控器状态
@router.get("/monitor-status", response_model=HttpResult[MonitorStatusResponse])
async def get_monitor_status(
    current_user_id: int = Depends(get_current_user),
):
    """
    获取任务监控器状态
    """
    try:
        from app.task_monitor import task_monitor
        
        status_info = {
            "running": task_monitor.is_running(),
            "poll_interval": task_monitor.poll_interval_seconds,
            "total_checks": task_monitor.total_checks,
            "total_updates": task_monitor.total_updates
        }
        
        return success(MonitorStatusResponse(**status_info))
        
    except Exception as e:
        logger.error(f"获取监控器状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取监控器状态失败")

# 转写结果响应模型
class TranscriptionResultResponse(BaseModel):
    file_name: str
    file_size_mb: float
    audio_duration_seconds: Optional[float] = None
    speaker_segments_count: int
    speakers: List[str]
    speaker_segments: List[dict]
    statistics: dict
    completed_at: str
    storage_type: str

@router.get("/transcription-result/{meeting_id}", response_model=HttpResult[TranscriptionResultResponse])
async def get_transcription_result(
    meeting_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取转写结果详情（从OSS获取完整数据）
    """
    try:
        # 获取会议信息
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()
        
        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在")
        
        # 检查任务状态
        if meeting.audioState != "finished":
            raise HTTPException(status_code=400, detail="转写任务尚未完成")
        
        # 解析存储在数据库中的结果
        if not meeting.celeryTaskResult:
            raise HTTPException(status_code=404, detail="转写结果不存在")
            
        try:
            stored_result = json.loads(meeting.celeryTaskResult)
        except json.JSONDecodeError:
            raise HTTPException(status_code=500, detail="转写结果格式错误")
        
        # 检查是否是OSS存储的结果
        if stored_result.get("storage_type") != "oss":
            raise HTTPException(status_code=400, detail="结果未存储在OSS中")
        
        result_url = stored_result.get("result_url")
        if not result_url:
            raise HTTPException(status_code=404, detail="OSS结果URL不存在")
        
        # 从OSS获取完整结果
        try:
            response = requests.get(result_url, timeout=30)
            response.raise_for_status()
            full_result = response.json()
        except requests.RequestException as e:
            logger.error(f"从OSS获取结果失败: {str(e)}")
            raise HTTPException(status_code=500, detail="获取完整转写结果失败")
        except json.JSONDecodeError:
            raise HTTPException(status_code=500, detail="OSS中的结果格式错误")
        
        # 构造响应
        return success(TranscriptionResultResponse(
            file_name=full_result.get("file_name", ""),
            file_size_mb=full_result.get("file_size_mb", 0.0),
            audio_duration_seconds=stored_result.get("audio_duration_seconds"),
            speaker_segments_count=len(full_result.get("speaker_segments", [])),
            speakers=stored_result.get("speakers", []),
            speaker_segments=full_result.get("speaker_segments", []),
            statistics=full_result.get("statistics", {}),
            completed_at=stored_result.get("completed_at", ""),
            storage_type="oss"
        ))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取转写结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取转写结果失败")

# 转写结果摘要响应模型
class TranscriptionSummaryResponse(BaseModel):
    file_name: str
    file_size_mb: float
    audio_duration_seconds: Optional[float] = None
    speaker_segments_count: int
    speakers: List[str]
    completed_at: str
    storage_type: str
    result_url: str

@router.get("/transcription-summary/{meeting_id}", response_model=HttpResult[TranscriptionSummaryResponse])
async def get_transcription_summary(
    meeting_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取转写结果摘要（仅数据库中的简化信息，不访问OSS）
    """
    try:
        # 获取会议信息
        meeting = db.query(Meeting).filter(
            Meeting.id == meeting_id,
            Meeting.user_id == current_user_id
        ).first()
        
        if not meeting:
            raise HTTPException(status_code=404, detail="会议不存在")
        
        # 检查任务状态
        if meeting.audioState != "finished":
            raise HTTPException(status_code=400, detail="转写任务尚未完成")
        
        # 解析存储在数据库中的结果
        if not meeting.celeryTaskResult:
            raise HTTPException(status_code=404, detail="转写结果不存在")
            
        try:
            stored_result = json.loads(meeting.celeryTaskResult)
        except json.JSONDecodeError:
            raise HTTPException(status_code=500, detail="转写结果格式错误")
        
        # 构造摘要响应
        return success(TranscriptionSummaryResponse(
            file_name=stored_result.get("file_name", ""),
            file_size_mb=stored_result.get("file_size_mb", 0.0),
            audio_duration_seconds=stored_result.get("audio_duration_seconds"),
            speaker_segments_count=stored_result.get("speaker_segments_count", 0),
            speakers=stored_result.get("speakers", []),
            completed_at=stored_result.get("completed_at", ""),
            storage_type=stored_result.get("storage_type", "oss"),
            result_url=stored_result.get("result_url", "")
        ))
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取转写结果摘要失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取转写结果摘要失败")