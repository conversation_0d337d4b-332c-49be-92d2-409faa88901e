#!/usr/bin/env python3
"""
启动所有 Worker 的脚本
包括执行 Worker 和监听 Worker
"""

import os
import sys
import subprocess
import time
import signal
import threading

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

class WorkerManager:
    def __init__(self):
        self.processes = []
        self.running = True
        
    def start_executor_worker(self):
        """启动执行 Worker"""
        print("🚀 启动执行 Worker...")
        
        # 设置环境变量
        env = os.environ.copy()
        env['WORKER_TYPE'] = 'executor'
        
        cmd = [
            'celery', '-A', 'app.tasks.celery_app', 'worker',
            '--loglevel=info',
            '--concurrency=2',
            '--queues=default',
            '--hostname=executor@%h',
            '--pidfile=/tmp/celery_executor.pid',
            '--logfile=/tmp/celery_executor.log',
        ]
        
        process = subprocess.Popen(cmd, env=env)
        self.processes.append(('执行 Worker', process))
        return process
    
    def start_monitor_worker(self):
        """启动监听 Worker"""
        print("🔍 启动监听 Worker...")
        
        # 设置环境变量
        env = os.environ.copy()
        env['WORKER_TYPE'] = 'monitor'
        
        cmd = [
            'celery', '-A', 'app.tasks.monitor.monitor_app', 'worker',
            '--loglevel=info',
            '--concurrency=1',
            '--queues=default',
            '--hostname=monitor@%h',
            '--pidfile=/tmp/celery_monitor.pid',
            '--logfile=/tmp/celery_monitor.log',
            '-E',  # 启用任务事件监控，这是信号监听的关键
        ]
        
        process = subprocess.Popen(cmd, env=env)
        self.processes.append(('监听 Worker', process))
        return process
    
    def start_monitor_beat(self):
        """启动监听 Worker 的定时任务调度器"""
        print("⏰ 启动监听 Worker 定时任务调度器...")
        
        # 设置环境变量
        env = os.environ.copy()
        env['WORKER_TYPE'] = 'monitor'
        
        cmd = [
            'celery', '-A', 'app.tasks.monitor.monitor_app', 'beat',
            '--loglevel=info',
            '--pidfile=/tmp/celery_monitor_beat.pid',
            '--logfile=/tmp/celery_monitor_beat.log',
            '--schedule=/tmp/celery_monitor_beat_schedule',
        ]
        
        process = subprocess.Popen(cmd, env=env)
        self.processes.append(('监听 Worker 定时任务调度器', process))
        return process
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            for name, process in self.processes:
                if process.poll() is not None:
                    print(f"⚠️ {name} 已停止，退出码: {process.returncode}")
                    if self.running:
                        print(f"🔄 尝试重启 {name}...")
                        if name == '执行 Worker':
                            self.start_executor_worker()
                        elif name == '监听 Worker':
                            self.start_monitor_worker()
                        elif name == '监听 Worker 定时任务调度器':
                            self.start_monitor_beat()
            time.sleep(5)
    
    def stop_all(self):
        """停止所有进程"""
        print("\n🛑 正在停止所有 Worker...")
        self.running = False
        
        for name, process in self.processes:
            try:
                print(f"停止 {name}...")
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                print(f"强制停止 {name}...")
                process.kill()
            except Exception as e:
                print(f"停止 {name} 时出错: {e}")
        
        print("✅ 所有 Worker 已停止")
    
    def start_all(self):
        """启动所有 Worker"""
        print("🚀 启动所有 Celery Worker...")
        
        # 设置环境变量
        os.environ.setdefault('CELERY_APP', 'app.tasks')
        
        # 启动所有 Worker
        self.start_executor_worker()
        time.sleep(2)  # 等待执行 Worker 启动
        
        self.start_monitor_worker()
        time.sleep(2)  # 等待监听 Worker 启动
        
        self.start_monitor_beat()
        time.sleep(2)  # 等待定时任务调度器启动
        
        print("✅ 所有 Worker 启动完成")
        print("📊 当前运行的 Worker:")
        for name, process in self.processes:
            print(f"  - {name} (PID: {process.pid})")
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self.monitor_processes)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # 等待中断信号
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop_all()

def main():
    """主函数"""
    manager = WorkerManager()
    
    # 注册信号处理器
    def signal_handler(signum, frame):
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        manager.start_all()
    except Exception as e:
        print(f"❌ 启动 Worker 失败: {e}")
        manager.stop_all()
        sys.exit(1)

if __name__ == '__main__':
    main() 