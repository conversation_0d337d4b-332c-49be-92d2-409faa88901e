# routers/auth.py
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm, OAuth2PasswordBearer
import httpx
from sqlalchemy.orm import Session
from datetime import timed<PERSON><PERSON>
from typing import Optional
from pydantic import BaseModel
from app.schemas import Token, LoginRequest  # 确保导入 LoginRequest

from app.models import  User,error, success, LoginToken,UserProfile, HttpResult
from app.utils import create_access_token, get_current_user  # 从utils导入
from config import cfg
from app.mysql import get_db

import logging

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/v1/api", tags=["auth"])

# 添加登录请求模型
class LoginRequest(BaseModel):
    username: str
    password: str

# 模型定义
class Token(BaseModel):
    access_token: str
    token_type: str
    user_id: int
    username: str

class UserCreate(BaseModel):
    username: str
    password: str
    

# JSON登录
@router.post("/auth/login", response_model=HttpResult[LoginToken])
async def login_json(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    '''
        用户登录
    '''
      
    UserHost = cfg.USER.HOST
    # 发送登录请求
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{UserHost}/api/v1/login",
            json={"login_type": "password", "account": login_data.username, "password": login_data.password}
        )
        
    if response.status_code != 200:
        return error("用户名密码错误")
    
    # 解析响应
    data = response.json()
    if data.get("code") != 200:
        return error(data.get("message"))
    
    token = data.get("data").get("token")

    if not token:
        return error("获取token失败")
    
    if token.startswith("Bearer "):
        token = token[7:]
        
    return success({"token": token, "username": login_data.username})

# 表单数据登录（兼容OAuth2标准，用于/docs页面授权）
@router.post("/auth/login/form", response_model=Token)
async def login_form(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    '''
    表单登录（兼容OAuth2标准）
    '''
    UserHost = cfg.USER.HOST
    # 发送登录请求
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{UserHost}/api/v1/login",
            json={"login_type": "password", "account": form_data.username, "password": form_data.password}
        )
        
    if response.status_code != 200:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 解析响应
    data = response.json()
    if data.get("code") != 200:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=data.get("message", "登录失败"),
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = data.get("data", {}).get("token")

    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="获取token失败",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if token.startswith("Bearer "):
        token = token[7:]
        
    return {
        "access_token": token,
        "token_type": "bearer",
        "user_id": 0,  # 由于使用外部认证，这里设置为0
        "username": form_data.username
    }

@router.post("/auth/register", response_model=HttpResult[LoginToken])
async def register(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    '''
        用户注册
    '''
    # 检查用户名是否已存在
    existing_user = db.query(User).filter(User.username == user_data.username).first()
    if existing_user:
        return error("用户名已被使用")
    
    # 创建新用户，不使用 email 字段
    new_user = User(
        username=user_data.username
        # 不包含 email 字段
    )
    new_user.set_password(user_data.password)
    
    try:
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # 创建访问令牌
        access_token_expires = timedelta(days=cfg.JWT_ACCESS_TOKEN_EXPIRE_DAYS)
        access_token = create_access_token(
            data={"sub": str(new_user.id)}, 
            expires_delta=access_token_expires
        )
        
        return success({
            "token": access_token,
            "username": new_user.username
        })
    except Exception as e:
        db.rollback()
        return error(f"注册用户失败: {str(e)}")

# 添加保护的测试路由
@router.get("/profile", response_model=HttpResult[UserProfile])
async def protected_test(current_user_id: int = Depends(get_current_user), db: Session = Depends(get_db)):
    """
    返回当前登录用户的用户名。
    """
    # 查询数据库获取用户
    user = db.query(User).filter(User.id == current_user_id).first()
    
    if not user:
        return error("用户不存在")
    
    return success({"username": user.username})
