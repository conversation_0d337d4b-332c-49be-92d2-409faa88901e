@echo off

for /f "tokens=2 delims==" %%a in ('findstr /c:"version" pyproject.toml') do set version=%%a
set version=%version:"=%
set version=%version: =%
echo Version: %version%

if defined version (
    echo Version: %version%
) else (
    echo Version not found.
    pause
    exit /b 1
)


echo building Docker image
docker compose -f docker-compose.prod.yml build
if %errorlevel% equ 0 (
    echo build success
) else (
    echo build failed
    pause
    exit /b 1
)

set DOCKER_PASSWORD=$WSambW2Rk5xNnE
echo %DOCKER_PASSWORD% | docker login --username=liwei@1340398900989581 --password-stdin registry.cn-hangzhou.aliyuncs.com
set TARGET_IMAGE=registry.cn-hangzhou.aliyuncs.com/sskw_dev/deepseekr1-backend:v%version%

docker tag dsr1-backend-prod:latest registry.cn-hangzhou.aliyuncs.com/sskw_dev/deepseekr1-backend:v%version%
echo Uploading registry.cn-hangzhou.aliyuncs.com/sskw_dev/deepseekr1-backend:v%version%

docker push %TARGET_IMAGE%
echo upload success


set USER=root
set SERVER=8.138.169.203
set REMOTE_PATH=/root/deepseekr1-pack
set LOCAL_COMPOSE_FILE=docker-compose.prod.yml
set TEMP_DIR=%cd%\temp
set TEMP_COMPOSE_FILE=%TEMP_DIR%\docker-compose.prod.yml
set LOCAL_ENV_FILE=.env


echo Creating temporary directory: %TEMP_DIR%
mkdir %TEMP_DIR%

echo Copying original docker-compose.yml to %TEMP_COMPOSE_FILE%
copy %LOCAL_COMPOSE_FILE% %TEMP_COMPOSE_FILE%

echo Modifying docker-compose.yml in temporary directory...
powershell -Command "$content = Get-Content '%TEMP_COMPOSE_FILE%' -Raw;$pattern = '(?s)(dsr1-backend-prod:.*?image:\s*)(.*?)(\r?\n)';$content = $content -replace $pattern, '${1}%TARGET_IMAGE%$3';Set-Content '%TEMP_COMPOSE_FILE%' $content -NoNewline;"

echo Docker Compose file updated in %TEMP_COMPOSE_FILE% with new image and tag.

:: echo Deleting temporary directory...
:: rmdir /s /q %TEMP_DIR%

echo Uploading docker-compose.prod.yml to %USER%@%SERVER%:%REMOTE_PATH%
scp %TEMP_COMPOSE_FILE% %USER%@%SERVER%:%REMOTE_PATH%
echo Uploading .env to %USER%@%SERVER%:%REMOTE_PATH%
scp %LOCAL_ENV_FILE% %USER%@%SERVER%:%REMOTE_PATH%

ssh  %USER%@%SERVER% "cd /root/deepseekr1-pack && docker pull %TARGET_IMAGE%"

ssh  %USER%@%SERVER% "cd /root/deepseekr1-pack && docker compose -f docker-compose.prod.yml up -d"
if %errorlevel% equ 0 (
    echo run deepseekr1-pack success
) else (
    echo run deepseekr1-pack failed
    pause
    exit /b 1
)

ssh  %USER%@%SERVER% "cd /root/deepseekr1-pack && docker exec deepseekr1-pack-dsr1-backend-prod-1 python -m alembic upgrade head"

echo deploy success

pause
