# FunASR 安装指南

## 问题描述

在 Windows 环境下安装 FunASR 时，可能会遇到 `aliyun-python-sdk-core` 包构建失败的问题。这是因为该包在 Windows 上需要编译，但缺少必要的构建工具。

## 解决方案

### 方案1: 使用提供的安装脚本（推荐）

#### 方法1.1: 使用批处理脚本
```bash
# 运行批处理脚本
install_funasr.bat
```

#### 方法1.2: 使用 Python 脚本
```bash
# 运行 Python 脚本
python install_funasr.py
```

### 方案2: 使用 Conda 环境（最稳定）

如果您有 Conda 环境，推荐使用此方案：

```bash
# 运行 conda 安装脚本
install_with_conda.bat
```

或者手动执行：

```bash
# 创建新环境
conda create -n funasr-env python=3.11 -y

# 激活环境
conda activate funasr-env

# 安装 PyTorch
conda install pytorch torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y

# 安装其他依赖
conda install -c conda-forge numpy scipy librosa soundfile -y

# 安装 FunASR
pip install funasr
```

### 方案3: 手动安装

#### 步骤1: 设置环境变量
```bash
set UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
set UV_PREFER_BINARY=true
```

#### 步骤2: 清理缓存
```bash
uv cache clean
```

#### 步骤3: 安装有问题的包
```bash
# 尝试安装预编译版本
uv pip install --no-build-isolation aliyun-python-sdk-core==2.16.0
```

#### 步骤4: 安装 FunASR
```bash
uv pip install funasr
```

### 方案4: 跳过有问题的依赖

如果上述方法都失败，可以尝试跳过依赖：

```bash
# 创建临时 requirements 文件
echo --no-deps > temp_requirements.txt
echo funasr>=1.2.6 >> temp_requirements.txt

# 安装 FunASR（跳过依赖）
uv pip install -r temp_requirements.txt

# 手动安装必要的依赖
uv pip install torch torchaudio numpy scipy librosa soundfile webrtcvad onnxruntime
```

## 验证安装

安装完成后，运行以下命令验证：

```python
python -c "import funasr; print('FunASR 安装成功!')"
```

## 常见问题

### 1. 构建工具缺失
**错误**: `Microsoft Visual C++ 14.0 or greater is required`
**解决**: 安装 Visual Studio Build Tools 或使用预编译的包

### 2. 网络问题
**错误**: 下载超时或连接失败
**解决**: 使用国内镜像源，如阿里云镜像

### 3. 权限问题
**错误**: 权限不足
**解决**: 以管理员身份运行命令提示符

### 4. Python 版本问题
**错误**: 版本不兼容
**解决**: 使用 Python 3.8-3.11 版本

## 推荐配置

### 系统要求
- Windows 10/11
- Python 3.8-3.11
- 8GB+ RAM
- NVIDIA GPU（可选，用于加速）

### 推荐环境
- 使用 Conda 环境
- Python 3.11
- PyTorch 2.0+
- CUDA 11.8（如果有 GPU）

## 使用示例

安装成功后，您可以在代码中使用：

```python
from funasr import AutoModel

# 初始化模型
model = AutoModel(
    model="paraformer-zh",
    model_hub="funasr",
    device="cuda"  # 或 "cpu"
)

# 转写音频
results = model.generate(input="audio.wav")
print(results)
```

## 技术支持

如果遇到其他问题，请：

1. 检查错误日志
2. 确认系统环境
3. 尝试不同的安装方案
4. 查看 FunASR 官方文档 