#!/usr/bin/env python3
"""
监听 Worker 启动脚本
专门负责监听任务状态并更新数据库
"""

import os
import sys
import subprocess

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def start_monitor_worker():
    """启动监听 Worker"""
    print("🔍 启动 Celery 监听 Worker...")
    
    # 设置环境变量
    os.environ.setdefault('CELERY_APP', 'app.tasks.monitor')
    # 明确标识这是监听 Worker，避免模型初始化
    os.environ['WORKER_TYPE'] = 'monitor'
    
    # 启动 worker
    cmd = [
        'celery', '-A', 'app.tasks.monitor.monitor_app', 'worker',
        '--loglevel=info',
        '--concurrency=1',  # 监听 Worker 只需要1个并发
        '--queues=default',  # 监听默认队列
        '--hostname=monitor@%h',  # Worker 主机名
        '-E',  # 启用任务事件监控，这是信号监听的关键
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n🛑 监听 Worker 已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动监听 Worker 失败: {e}")
        sys.exit(1)

def start_monitor_beat():
    """启动监听 Worker 的定时任务调度器"""
    print("⏰ 启动 Celery 监听 Worker 定时任务调度器...")
    
    # 设置环境变量
    os.environ.setdefault('CELERY_APP', 'app.tasks.monitor')
    # 明确标识这是监听 Worker，避免模型初始化
    os.environ['WORKER_TYPE'] = 'monitor'
    
    # 启动 beat
    cmd = [
        'celery', '-A', 'app.tasks.monitor.monitor_app', 'beat',
        '--loglevel=info'
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n🛑 监听 Worker 定时任务调度器已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动监听 Worker 定时任务调度器失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='启动监听 Worker')
    parser.add_argument('--beat', action='store_true', help='启动定时任务调度器')
    
    args = parser.parse_args()
    
    if args.beat:
        start_monitor_beat()
    else:
        start_monitor_worker() 