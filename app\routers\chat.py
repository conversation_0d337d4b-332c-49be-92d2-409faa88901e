# routers/chat.py
from fastapi import APIRouter, Depends, HTTPException, Request, Response, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import requests
import json
import time
import traceback
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from app.models import Conversation, Message, User
from app.utils import get_current_user
import logging
import aiohttp
from config import get_db,SessionLocal


# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api", tags=["chat"])

# 修改后的请求模型 - 只接收单条用户消息
class ChatRequest(BaseModel):
    conversation_id: int
    message: str  # 只接收单条用户消息内容
    model: str # 模型Id

# 调试消息响应模型
class DebugMessageResponse(BaseModel):
    id: int
    conversation_id: int
    role: str
    content: str
    reasoning_content: Optional[str]
    content_length: int
    reasoning_length: int
    created_at: Optional[str]

def format_reasoning_content(reasoning_text: str, preserve_first_newline: bool = True) -> str:
    """
    格式化推理内容，保留格式和换行符
    
    Args:
        reasoning_text: 原始推理内容
        preserve_first_newline: 是否保留开头的换行符
    
    Returns:
        格式化后的推理内容
    """
    import re
    
    # 首先，保存原始文本中第一个换行符的位置
    first_newline_pos = reasoning_text.find('\n')
    has_first_newline = first_newline_pos >= 0
    
    # 1. 标准化换行符
    reasoning_text = reasoning_text.replace('\r\n', '\n').replace('\r', '\n')
    
    # 2. 限制连续换行符为最多2个
    reasoning_text = re.sub(r'\n{3,}', '\n\n', reasoning_text)
    
    # 3. 移除段落边界的多余空白
    reasoning_text = re.sub(r'(?<=\n\n)\s+(?=\S)', '', reasoning_text)
    reasoning_text = re.sub(r'(?<=\S)\s+(?=\n\n)', '', reasoning_text)
    
    # 4. 清理空格+换行符组合
    reasoning_text = re.sub(r' +\n(?!\n)', '\n', reasoning_text)
    reasoning_text = re.sub(r'\n(?!\n) +', '\n', reasoning_text)
    
    # 5. 修剪文本两端的空白
    reasoning_text = reasoning_text.strip()
    
    # 6. 确保第一个换行符被保留
    if has_first_newline and preserve_first_newline:
        # 找到第一个非空白字符的位置
        first_non_space = re.search(r'\S', reasoning_text)
        if first_non_space:
            # 在第一个非空白字符之前插入换行符
            pos = first_non_space.start()
            reasoning_text = reasoning_text[:pos] + '\n' + reasoning_text[pos:]
    
    return reasoning_text


import threading


        
# 修改后的聊天路由
@router.post("/chat")
def chat(
    chat_request: ChatRequest, 
    request: Request,
    background_tasks: BackgroundTasks,
    current_user_id: int = Depends(get_current_user), 
    db: Session = Depends(get_db)
):
    '''
      发起聊天 - 后端管理对话历史
    '''
    start_time = time.time()
    
    # 参数已经通过Pydantic模型验证
    conversation_id = chat_request.conversation_id
    user_message = chat_request.message.strip()
    
    logger.debug(f"收到请求头: {dict(request.headers)}")
    logger.debug(f"收到用户消息: conversation_id={conversation_id}, 消息长度={len(user_message)}")
    
    # 对话权限验证
    try:
        conv = db.query(Conversation).filter(
            Conversation.id == conversation_id, 
            Conversation.user_id == current_user_id
        ).first()
        
        if not conv:
            raise HTTPException(status_code=403, detail="对话不存在或无权访问")
    except Exception as e:
        logger.error(f"查询对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail="系统错误")
    
    # 保存用户消息
    user_msg_id = None
    try:
        if user_message:
            # 保存用户消息
            user_msg = Message(
                conversation_id=conversation_id,
                role='user',
                content=user_message,
                created_at=datetime.utcnow()
            )
            db.add(user_msg)
            
            # 更新对话的更新时间
            conv.updated_at = datetime.utcnow()
            
            db.commit()  # 立即提交
            user_msg_id = user_msg.id
            logger.info(f"用户消息保存成功 | ID={user_msg.id} | 长度={len(user_message)}")
        else:
            logger.warning("用户消息内容为空，跳过保存")
                    
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"用户消息保存失败 | 错误={str(e)}")
        # 继续处理，不返回错误
    except Exception as e:
        db.rollback()
        logger.error(f"未知错误 | 错误类型={type(e).__name__} | 详情={str(e)}")
        logger.error(traceback.format_exc())
        # 继续处理，不返回错误
    
    # 从数据库获取历史消息
    messages = []
    try:
        # 获取最近10条消息（不包括刚刚添加的消息）
        history_query = db.query(Message).filter(
            Message.conversation_id == conversation_id
        )
        
        # 如果有新消息ID，排除它
        if user_msg_id:
            history_query = history_query.filter(Message.id != user_msg_id)
            
        # 获取最近的消息并按时间正序排列
        recent_messages = history_query.order_by(Message.created_at.desc()).limit(10).all()
        recent_messages.reverse()  # 反转顺序，使其按时间正序排列
        
        # 转换为DeepSeek API需要的格式
        for msg in recent_messages:
            messages.append({
                "role": msg.role,
                "content": msg.content
            })
            
        logger.info(f"已从数据库加载 {len(messages)} 条历史消息")
    except Exception as e:
        logger.error(f"加载历史消息失败: {str(e)}")
        # 继续处理，即使没有历史消息
    
    # 添加用户的新消息
    if user_message:
        messages.append({
            "role": "user",
            "content": user_message
        })
    
    # DeepSeek请求
    request_id = f"req_{int(time.time())}_{conversation_id}"

    # 预先创建AI消息
    assistant_msg_id = None
    try:
        assistant_msg = Message(
            conversation_id=conversation_id,
            role='assistant',
            content='',  # 初始为空内容
            reasoning_content='',  # 初始为空内容
            created_at=datetime.utcnow()
        )
        db.add(assistant_msg)
        db.commit()
        assistant_msg_id = assistant_msg.id
        logger.info(f"预创建AI消息 | ID={assistant_msg_id}")
    except Exception as e:
        logger.error(f"预创建AI消息失败 | 错误={str(e)}")
        # 即使这里失败，我们仍然继续

    async def generate_streaming_response():
        """流式响应生成器，使用aiohttp的异步流式处理"""
        content_buffer = []
        reasoning_buffer = []
        chunk_count = 0
        stream_start = time.time()
        has_reasoning = False
        
        async with aiohttp.ClientSession() as session:
            try:
                logger.info(f"请求AI服务 | ID={request_id} | 消息数={len(messages)}")
                dpr1 = {
                        "model": "deepseek-r1", #deepseek-reasoner
                        "token": "sskwSSKWopenAIdeepSEEK",
                        "url": "http://139.155.86.17:8000/v1/chat/completions"
                }
                models = {
                    "deepseek-r1": dpr1
                }
                model = models.get(chat_request.model, dpr1)

                print("using model=", model)

                async with session.post(
                    model["url"],
                    json={
                        "model": model["model"],
                        "messages": messages,
                        "stream": True,
                        "request_id": request_id
                    },
                    headers={"Authorization": f"Bearer {model['token']}"},
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as resp:
                    if resp.status != 200:
                        error_text = await resp.text()
                        logger.error(f"AI服务响应错误 | 状态码={resp.status} | 错误={error_text}")
                        yield f"data: {{\"error\": \"AI服务返回错误: {resp.status}\"}}\n\n"
                        return
                    
                    logger.info(f"AI服务响应状态码: {resp.status}")
                    
                    async for line in resp.content:
                        if not line:
                            continue
                            
                        try:
                            decoded = line.decode('utf-8').strip()
                            if not decoded.startswith('data:'):
                                continue
                                
                            yield f"{decoded}\n\n"
                            
                            chunk = decoded[5:].strip()
                            if chunk == '[DONE]':
                                logger.info(
                                    f"流式响应完成 | RequestID={request_id} | "
                                    f"耗时={time.time()-stream_start:.2f}s"
                                )
                                continue
                            
                            try:
                                chunk_data = json.loads(chunk)
                                delta = chunk_data.get('choices', [{}])[0].get('delta', {})
                                chunk_count += 1
                                
                                # 处理内容部分
                                if content := delta.get('content', ''):
                                    if isinstance(content, str):
                                        content_buffer.append(content)
                                
                                # 处理推理部分
                                reasoning_fields = ['reasoning', 'reasoning_content']
                                if reasoning := next((delta.get(f) for f in reasoning_fields if f in delta), None):
                                    if isinstance(reasoning, str) and reasoning.strip():
                                        reasoning_buffer.append(reasoning)
                                        has_reasoning = True
                                        logger.debug(f"收到推理内容片段，长度: {len(reasoning)}")
                                        
                            except json.JSONDecodeError:
                                logger.error(f"JSON解析失败: {chunk}")
                            except KeyError as e:
                                logger.error(f"数据结构异常，缺少字段: {str(e)}")
                            except Exception as e:
                                logger.error(f"数据处理异常: {str(e)}")
                                
                        except UnicodeDecodeError:
                            logger.error(f"Unicode解码错误: {line}")
                            yield "data: {\"content\": \"[解码错误]\"}\n\n"
                        except Exception as e:
                            logger.error(f"行处理异常: {str(e)}")
                            
            except Exception as e:
                logger.error(f"AI服务连接失败 | RequestID={request_id} | 错误={str(e)}")
                yield f"data: {{\"error\": \"AI服务暂时不可用: {str(e)}\"}}\n\n"
            finally:
                # 保存响应到数据库
                try:
                    assistant_content = ''.join(content_buffer).strip()
                    assistant_reasoning = format_reasoning_content(''.join(reasoning_buffer), preserve_first_newline=True)
                    
                    logger.info(
                        f"准备保存AI响应 | 内容长度={len(assistant_content)} | "
                        f"推理长度={len(assistant_reasoning)} | 有推理内容={has_reasoning}"
                    )
                    
                    if assistant_content:
                        background_tasks.add_task(
                            save_assistant_response,
                            conversation_id=conversation_id,
                            assistant_msg_id=assistant_msg_id,
                            assistant_content=assistant_content,
                            assistant_reasoning=assistant_reasoning,
                            chunk_count=chunk_count,
                            request_id=request_id
                        )
                    else:
                        logger.warning(f"空响应未保存 | RequestID={request_id}")
                        
                except Exception as e:
                    logger.error(f"保存AI响应失败 | RequestID={request_id} | 错误={str(e)}")
                    logger.error(traceback.format_exc())
                
                logger.info(f"请求完成 | 总耗时={time.time()-start_time:.2f}s")
    
    # 返回流式响应
    return StreamingResponse(
        generate_streaming_response(),
        media_type='text/event-stream',
        headers={
            'X-Conversation-ID': str(conversation_id),
            'X-Request-ID': request_id,
            'Cache-Control': 'no-cache, no-transform',
            'Connection': 'keep-alive',
            "X-Accel-Buffering": "no"  # 特别针对Nginx的指令
        }
    )

# 后台任务：保存助手响应 (保持不变)
async def save_assistant_response(
    conversation_id: int,
    assistant_msg_id: Optional[int],
    assistant_content: str,
    assistant_reasoning: str,
    chunk_count: int,
    request_id: str
):
    # 创建新的数据库会话
    db = SessionLocal()
    try:
        # 尝试更新已创建的消息
        if assistant_msg_id:
            msg_to_update = db.query(Message).filter(Message.id == assistant_msg_id).first()
            if msg_to_update:
                msg_to_update.content = assistant_content
                msg_to_update.reasoning_content = assistant_reasoning
                db.commit()
                logger.info(f"AI响应已更新 | ID={assistant_msg_id} | 内容长度={len(assistant_content)} | 推理长度={len(assistant_reasoning)} | 块数={chunk_count}")
            else:
                logger.error(f"找不到ID为{assistant_msg_id}的消息记录")
                # 创建新消息
                new_msg = Message(
                    conversation_id=conversation_id,
                    role='assistant',
                    content=assistant_content,
                    reasoning_content=assistant_reasoning,
                    created_at=datetime.utcnow()
                )
                db.add(new_msg)
                db.commit()
                logger.info(f"创建新AI响应 | ID={new_msg.id} | 内容长度={len(assistant_content)} | 推理长度={len(assistant_reasoning)}")
        else:
            # 直接创建新消息
            new_msg = Message(
                conversation_id=conversation_id,
                role='assistant',
                content=assistant_content,
                reasoning_content=assistant_reasoning,
                created_at=datetime.utcnow()
            )
            db.add(new_msg)
            db.commit()
            logger.info(f"创建新AI响应 | ID={new_msg.id} | 内容长度={len(assistant_content)} | 推理长度={len(assistant_reasoning)}")
        
        # 更新对话的更新时间
        db.query(Conversation).filter(Conversation.id == conversation_id).update(
            {'updated_at': datetime.utcnow()}
        )
        db.commit()
    except Exception as e:
        logger.error(f"保存AI响应失败 | RequestID={request_id} | 错误={str(e)}")
        logger.error(traceback.format_exc())
        db.rollback()
    finally:
        db.close()
