#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTorch GPU 测试脚本
用于验证PyTorch是否正确安装并可以使用GPU
"""

import torch
import sys
import platform

def print_system_info():
    """打印系统信息"""
    print("=" * 50)
    print("系统信息:")
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"PyTorch版本: {torch.__version__}")
    print("=" * 50)

def test_cuda_availability():
    """测试CUDA可用性"""
    print("\n1. CUDA可用性测试:")
    print("-" * 30)
    
    # 检查CUDA是否可用
    cuda_available = torch.cuda.is_available()
    print(f"CUDA是否可用: {cuda_available}")
    
    if cuda_available:
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"cuDNN版本: {torch.backends.cudnn.version()}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        
        # 显示每个GPU的信息
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
    else:
        print("警告: CUDA不可用，将使用CPU进行计算")
    
    return cuda_available

def test_gpu_computation():
    """测试GPU计算功能"""
    print("\n2. GPU计算测试:")
    print("-" * 30)
    
    # 创建测试张量
    print("创建测试张量...")
    x = torch.randn(1000, 1000)
    y = torch.randn(1000, 1000)
    
    # CPU计算
    print("在CPU上进行矩阵乘法...")
    start_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
    end_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
    
    if torch.cuda.is_available():
        start_time.record()
    
    cpu_start = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
    cpu_end = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
    
    if torch.cuda.is_available():
        cpu_start.record()
    
    cpu_result = torch.mm(x, y)
    
    if torch.cuda.is_available():
        cpu_end.record()
        torch.cuda.synchronize()
        cpu_time = cpu_start.elapsed_time(cpu_end)
        print(f"CPU计算时间: {cpu_time:.2f} ms")
    
    # GPU计算（如果可用）
    if torch.cuda.is_available():
        print("在GPU上进行矩阵乘法...")
        x_gpu = x.cuda()
        y_gpu = y.cuda()
        
        if torch.cuda.is_available():
            start_time.record()
        
        gpu_result = torch.mm(x_gpu, y_gpu)
        
        if torch.cuda.is_available():
            end_time.record()
            torch.cuda.synchronize()
            gpu_time = start_time.elapsed_time(end_time)
            print(f"GPU计算时间: {gpu_time:.2f} ms")
            
            if cpu_time and gpu_time:
                speedup = cpu_time / gpu_time
                print(f"GPU加速比: {speedup:.2f}x")
        
        # 验证结果一致性
        print("验证CPU和GPU结果一致性...")
        diff = torch.abs(cpu_result - gpu_result.cpu()).max().item()
        print(f"最大误差: {diff:.2e}")
        
        if diff < 1e-5:
            print("✓ CPU和GPU计算结果一致")
        else:
            print("✗ CPU和GPU计算结果不一致")
            
        # 清理GPU内存
        del x_gpu, y_gpu, gpu_result
        torch.cuda.empty_cache()
    else:
        print("跳过GPU计算测试（CUDA不可用）")

def test_memory_management():
    """测试GPU内存管理"""
    if not torch.cuda.is_available():
        print("\n3. GPU内存管理测试: 跳过（CUDA不可用）")
        return
    
    print("\n3. GPU内存管理测试:")
    print("-" * 30)
    
    # 显示初始内存状态
    print(f"初始GPU内存使用: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
    print(f"初始GPU内存缓存: {torch.cuda.memory_reserved() / 1024**2:.1f} MB")
    
    # 分配大张量
    print("分配大张量...")
    large_tensor = torch.randn(5000, 5000).cuda()
    print(f"分配后GPU内存使用: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
    
    # 删除张量并清理缓存
    del large_tensor
    torch.cuda.empty_cache()
    print(f"清理后GPU内存使用: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")

def test_torchaudio():
    """测试torchaudio功能"""
    print("\n4. TorchAudio测试:")
    print("-" * 30)
    
    try:
        import torchaudio
        print(f"TorchAudio版本: {torchaudio.__version__}")
        
        # 测试基本功能
        sample_rate = 16000
        duration = 1.0  # 1秒
        num_samples = int(sample_rate * duration)
        
        # 生成测试音频
        test_audio = torch.randn(num_samples)
        print(f"生成测试音频: {num_samples} 采样点")
        
        if torch.cuda.is_available():
            test_audio_gpu = test_audio.cuda()
            print("✓ TorchAudio支持GPU张量")
        else:
            print("TorchAudio使用CPU张量")
            
        print("✓ TorchAudio功能正常")
        
    except ImportError:
        print("✗ TorchAudio未安装")
    except Exception as e:
        print(f"✗ TorchAudio测试失败: {e}")

def main():
    """主函数"""
    print("PyTorch GPU 测试开始")
    print("=" * 50)
    
    # 打印系统信息
    print_system_info()
    
    # 测试CUDA可用性
    cuda_available = test_cuda_availability()
    
    # 测试GPU计算
    test_gpu_computation()
    
    # 测试内存管理
    test_memory_management()
    
    # 测试torchaudio
    test_torchaudio()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    if cuda_available:
        print("✓ PyTorch GPU 配置成功！")
        print("✓ 可以正常使用GPU进行计算")
    else:
        print("⚠ PyTorch GPU 配置存在问题")
        print("建议检查:")
        print("  1. NVIDIA驱动是否正确安装")
        print("  2. CUDA工具包是否正确安装")
        print("  3. PyTorch版本是否与CUDA版本匹配")
    print("=" * 50)

if __name__ == "__main__":
    main() 