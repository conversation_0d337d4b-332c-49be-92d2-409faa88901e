# 任务监控器使用说明

## 概述

新的任务监控器是一个后台线程，在应用启动时自动开启，用于轮询查询meeting中的celery任务执行情况，并自动更新结果到meeting表中。这个功能替代了原来的`app.task.monitor`中的工作。

## 功能特点

### 1. 自动监控
- ✅ 应用启动时自动开启后台监控线程
- ✅ 应用关闭时自动停止监控线程
- ✅ 支持优雅关闭，避免数据丢失

### 2. 智能轮询
- ✅ 默认30秒轮询间隔，可通过环境变量配置
- ✅ 只监控状态为`pending`或`running`的任务
- ✅ 自动检测任务状态变化并更新数据库

### 3. 状态同步
- ✅ 任务成功 → `audioState`: `finished`, `celeryTaskStatus`: `finished`
- ✅ 任务失败 → `audioState`: `failed`, `celeryTaskStatus`: `failed`
- ✅ 任务开始 → `audioState`: `handing`, `celeryTaskStatus`: `running`
- ✅ 任务取消 → `audioState`: `canceled`, `celeryTaskStatus`: `canceled`

### 4. 监控统计
- ✅ 记录总检查次数和更新次数
- ✅ 提供API接口查看监控状态
- ✅ 详细的日志记录和错误处理

### 5. 智能数据处理 🆕
- ✅ **自动处理长文本**: 智能截断超过60KB的任务结果
- ✅ **保留重要信息**: 优先保留统计信息、状态和关键数据
- ✅ **摘要生成**: 对转写结果生成摘要，包含说话人统计和片段预览
- ✅ **错误恢复**: 防止因结果过长导致的数据库错误

## 配置选项

### 环境变量配置

```bash
# 设置轮询间隔（秒），默认30秒
export TASK_MONITOR_INTERVAL=30
```

### 代码配置

```python
# 在 app/task_monitor.py 中
task_monitor = TaskMonitor(poll_interval=60)  # 自定义60秒间隔
```

## API接口

### 查看监控器状态

```http
GET /api/meeting/monitor-status
Authorization: Bearer your_token
```

**响应示例：**
```json
{
  "code": 200,
  "message": "",
  "data": {
    "running": true,
    "poll_interval": 30,
    "total_checks": 150,
    "total_updates": 12
  }
}
```

## 长文本处理机制 🆕

当Celery任务结果超过60KB时，系统会自动进行智能截断：

### 处理策略

1. **检查长度**: 检查JSON字符串的UTF-8编码长度
2. **智能截断**: 如果超长，保留重要信息进行截断
3. **摘要生成**: 生成包含关键统计信息的摘要
4. **错误恢复**: 即使截断失败也能正常处理

### 截断后的数据格式

```json
{
  "truncated": true,
  "truncated_at": "2024-01-01T12:00:00Z",
  "original_keys": ["file_name", "statistics", "speaker_segments"],
  "file_name": "meeting_audio.mp3",
  "file_size_mb": 125.6,
  "statistics": {
    "transcription_time": 245.6,
    "total_duration": 3600
  },
  "speaker_segments_count": 156,
  "speaker_segments_preview": [
    {
      "speaker": "speaker_1",
      "start_time": 0.0,
      "end_time": 5.2,
      "text": "大家好，今天我们来讨论..."
    }
  ],
  "speakers": ["speaker_1", "speaker_2", "speaker_3"]
}
```

## 监控流程

```mermaid
graph TD
    A[应用启动] --> B[启动任务监控器]
    B --> C[开启后台线程]
    C --> D[轮询数据库]
    D --> E[查询pending/running任务]
    E --> F[检查Celery任务状态]
    F --> G{任务状态变化?}
    G -->|是| H[获取任务结果]
    G -->|否| I[等待下次轮询]
    H --> J{结果长度检查}
    J -->|超长| K[智能截断]
    J -->|正常| L[直接存储]
    K --> M[更新数据库]
    L --> M
    M --> I
    I --> N[休眠30秒]
    N --> D
    O[应用关闭] --> P[停止监控器]
    P --> Q[等待线程结束]
```

## 监控的任务状态

| Celery状态 | audioState | celeryTaskStatus | 说明 |
|-----------|------------|------------------|------|
| PENDING   | uploaded   | pending          | 任务等待执行 |
| STARTED   | handing    | running          | 任务开始执行 |
| PROGRESS  | handing    | running          | 任务执行中（带进度） |
| SUCCESS   | finished   | finished         | 任务成功完成 |
| FAILURE   | failed     | failed           | 任务执行失败 |
| REVOKED   | canceled   | canceled         | 任务被取消 |

## 日志记录

### 启动/停止日志
```
INFO: ✅ 任务监控器已启动，轮询间隔: 30秒
INFO: 🛑 任务监控器已停止，总检查次数: 150，总更新次数: 12
```

### 任务状态更新日志
```
INFO: 任务 celery-task-uuid 成功完成，会议ID: 123
INFO: 会议 123 状态更新: handing/running -> finished/finished
```

### 长文本处理日志 🆕
```
INFO: 会议 123 任务结果过长，已进行智能截断
DEBUG: 截断前长度: 128KB, 截断后长度: 58KB
```

### 统计日志
```
INFO: 📊 监控统计: 检查次数=100, 更新次数=8
INFO: 📊 本次监控更新了 2 个任务状态
```

## 替代的功能

此任务监控器替代了以下原有功能：

1. **app/tasks/monitor.py** 中的定时任务监控
2. **Celery Beat** 的定时调度任务
3. **手动状态查询** 的需求

## 优势对比

| 功能 | 原monitor.py | 新TaskMonitor |
|------|-------------|---------------|
| 部署复杂度 | 需要单独的Worker+Beat | 集成在主应用中 |
| 资源消耗 | 需要额外进程 | 单个后台线程 |
| 实时性 | 依赖定时任务 | 持续轮询 |
| 错误恢复 | 需要手动重启 | 自动重试 |
| 配置复杂度 | 需要配置多个组件 | 简单的环境变量 |
| 长文本处理 | 可能导致数据库错误 | ✅ 智能截断处理 |

## 故障排除

### 1. 监控器未启动
检查应用启动日志：
```bash
grep "任务监控器" app.log
```

### 2. 任务状态未更新
1. 检查Celery连接是否正常
2. 查看监控器统计信息
3. 检查数据库连接

### 3. 调整轮询频率
```bash
# 设置更短的轮询间隔
export TASK_MONITOR_INTERVAL=10
```

### 4. 数据库字段长度错误 🆕
如果遇到"Data too long for column"错误：
1. 检查是否使用了最新版本的监控器
2. 查看日志中的截断处理记录
3. 验证`safe_format_result_for_storage`函数是否被正确调用

## 注意事项

1. **线程安全**: 监控器使用独立的数据库会话，确保线程安全
2. **优雅关闭**: 应用关闭时会等待监控线程结束，最长5秒超时
3. **错误恢复**: 单次监控失败不会影响后续监控
4. **资源管理**: 每次检查后会主动关闭数据库连接
5. **长文本处理**: 自动处理超长结果，防止数据库错误 🆕

## 性能影响

- **CPU使用**: 极低，只在轮询时短暂占用
- **内存使用**: 约1-2MB额外内存
- **数据库连接**: 每次检查使用独立连接，用完即关闭
- **网络开销**: 轮询频率低，网络影响可忽略
- **存储开销**: 长文本智能截断，减少存储空间占用 🆕

## 扩展建议

1. **监控告警**: 可以集成钉钉、微信等告警通知
2. **性能优化**: 可以批量查询和更新，减少数据库操作
3. **监控面板**: 可以开发Web界面展示监控状态
4. **历史统计**: 可以记录更详细的统计信息
5. **完整结果存储**: 可以将完整的长文本结果存储到文件系统或对象存储 🆕 