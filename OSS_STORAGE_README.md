# OSS 转写结果存储方案

## 概述

为了解决大型转写结果导致的数据库存储问题，我们实现了基于阿里云OSS的存储方案。完整的转写结果将自动上传到OSS，而数据库中只保存简化的元数据和OSS访问URL。

## 🏗️ 架构说明

### 数据流程
```
音频转写 → 完整结果生成 → 上传到OSS → 数据库保存简化结果
                ↓
            包含OSS URL的元数据
```

### 存储策略
- **OSS存储**: 完整的转写结果（包含所有speaker_segments）
- **数据库存储**: 简化的元数据 + OSS URL引用

## 🔧 实现细节

### 1. 转写任务流程 (executor.py)

当转写任务完成时：

1. **生成完整结果**: 包含所有转写数据
2. **上传到OSS**: 使用时间戳和会议ID生成唯一文件名
3. **简化结果**: 创建包含元数据和OSS URL的简化版本
4. **保存到数据库**: 只存储简化结果

```python
# 完整结果示例（上传到OSS）
{
    "file_name": "meeting_audio.mp3",
    "file_size_mb": 25.6,
    "speaker_segments": [
        {
            "start_time": 0.0,
            "end_time": 3.5,
            "speaker": "speaker_1",
            "text": "欢迎参加今天的会议..."
        },
        // ... 更多转写片段
    ],
    "statistics": {...}
}

# 简化结果示例（保存到数据库）
{
    "file_name": "meeting_audio.mp3",
    "file_size_mb": 25.6,
    "speaker_segments_count": 156,
    "speakers": ["speaker_1", "speaker_2"],
    "audio_duration_seconds": 3600,
    "result_url": "https://bucket.oss-region.com/path/to/result.json",
    "storage_type": "oss",
    "completed_at": "2024-01-01T12:00:00Z"
}
```

### 2. OSS文件组织结构

```
transcription_results/
├── 2024/
│   ├── 01/
│   │   ├── 1_20240101_120000.json    # 会议ID_时间戳.json
│   │   ├── 2_20240101_130000.json
│   │   └── ...
│   ├── 02/
│   └── ...
└── 2025/
```

### 3. API接口

#### 获取转写结果摘要
```http
GET /api/meeting/transcription-summary/{meeting_id}
```
返回数据库中的简化信息，不访问OSS，响应速度快。

#### 获取完整转写结果
```http
GET /api/meeting/transcription-result/{meeting_id}
```
从OSS获取完整的转写数据，包含所有speaker_segments。

## 🚀 使用指南

### 1. 配置OSS

确保配置文件中有正确的OSS设置：

```yaml
OSS:
  ACCESS_KEY_ID: 'your_access_key'
  ACCESS_KEY_SECRET: 'your_secret_key' 
  ENDPOINT: 'oss-cn-region.aliyuncs.com'
  BUCKET_NAME: 'your_bucket_name'
  REGION: 'cn-region'
```

### 2. 启动转写任务

使用现有的创建会议API，系统会自动：
- 处理音频文件
- 生成转写结果
- 上传完整结果到OSS
- 在数据库中保存简化版本

### 3. 获取转写结果

#### 快速预览（推荐）
```bash
curl "http://localhost:8000/api/meeting/transcription-summary/123"
```

返回基本信息和统计数据，不包含具体转写内容。

#### 完整数据
```bash
curl "http://localhost:8000/api/meeting/transcription-result/123"
```

返回完整的转写结果，包含所有speaker_segments。

## 📊 性能对比

| 指标 | 旧方案 | OSS方案 |
|------|--------|---------|
| **数据库存储大小** | 60KB-几MB | < 2KB |
| **查询响应时间** | 100-500ms | 50-100ms |
| **完整数据获取** | 直接查询 | OSS + 100-200ms |
| **存储成本** | 数据库成本高 | OSS成本低 |
| **扩展性** | 受数据库限制 | 几乎无限制 |

## 🛡️ 安全性

### 1. OSS访问控制
- 使用阿里云RAM账号进行访问控制
- 文件设置为公开读取（便于前端直接访问）
- 通过API鉴权控制数据访问权限

### 2. 数据备份
- OSS自动提供多重备份
- 可配置跨区域备份
- 支持版本控制

## 🔍 监控和维护

### 1. 日志监控
```bash
# 查看OSS上传日志
grep "转写结果已上传到OSS" app.log

# 查看数据简化效果
grep "原始数据大小.*简化数据大小" app.log
```

### 2. 存储统计
- 通过阿里云控制台监控OSS使用量
- 定期清理过期的转写结果文件

### 3. 故障处理

#### OSS上传失败
```python
# 检查OSS配置
# 验证访问密钥
# 确认bucket权限
```

#### 结果获取失败
```python
# 检查OSS URL有效性
# 验证文件是否存在
# 确认网络连接
```

## 🔄 迁移指南

### 从旧方案迁移

1. **保持兼容性**: 新方案向后兼容旧的存储格式
2. **逐步迁移**: 新任务自动使用OSS存储
3. **数据清理**: 可选择性地迁移历史数据到OSS

### 数据格式识别
```python
# 通过storage_type字段识别存储类型
if result.get("storage_type") == "oss":
    # OSS存储的结果
    url = result.get("result_url")
else:
    # 传统数据库存储的结果
    segments = result.get("speaker_segments", [])
```

## 📈 未来优化

### 1. 压缩优化
- 对JSON结果进行GZIP压缩
- 减少网络传输时间

### 2. 缓存策略
- 热门结果的本地缓存
- Redis缓存频繁访问的数据

### 3. CDN加速
- 通过阿里云CDN加速OSS访问
- 提高全球访问速度

## 🆘 故障排除

### 常见问题

1. **转写结果上传失败**
   - 检查OSS配置
   - 验证网络连接
   - 确认bucket权限

2. **无法获取完整结果**
   - 验证OSS URL有效性
   - 检查文件是否存在
   - 确认访问权限

3. **数据库中无简化结果**
   - 检查任务是否完成
   - 验证executor.py逻辑
   - 查看任务执行日志

### 调试工具

```bash
# 测试OSS连接
python -c "
import oss2
from config import cfg
auth = oss2.Auth(cfg.OSS.ACCESS_KEY_ID, cfg.OSS.ACCESS_KEY_SECRET)
bucket = oss2.Bucket(auth, cfg.OSS.ENDPOINT, cfg.OSS.BUCKET_NAME)
print(bucket.get_bucket_info())
"

# 检查特定文件
curl -I "https://bucket.oss-region.com/path/to/result.json"
```

## 📋 总结

OSS存储方案有效解决了：
- ✅ 数据库存储大小限制
- ✅ 查询性能问题  
- ✅ 系统扩展性问题
- ✅ 存储成本问题

同时保持了：
- ✅ API接口的向后兼容性
- ✅ 数据的安全性和可靠性
- ✅ 系统的简单性和易维护性 