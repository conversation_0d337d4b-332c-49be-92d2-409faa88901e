# routers/summary.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

from app.models import HttpResult, success, Conversation
from app.utils import get_current_user
import logging
from config import get_db

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/summary", tags=["summary"])

# 请求模型
class SummaryRequest(BaseModel):
    conversation_id: int = Field(..., description="对话ID (整数)")
    message: str = Field(..., description="用户消息")

# 响应模型
class SummaryResponse(BaseModel):
    message: str = Field(..., description="消息摘要（前20个字符）")
    original_length: int = Field(..., description="原始消息长度")
    conversation_id: int = Field(..., description="对话ID (整数)")

@router.post("", response_model=HttpResult[SummaryResponse])
async def summarize_message(
    request: SummaryRequest,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    接收整数对话ID和消息，返回消息的摘要（前20个字符）并更新对话标题
    """
    try:
        # 获取请求数据
        conversation_id = request.conversation_id
        message = request.message
        
        # 记录请求
        logger.info(f"收到消息摘要请求 | 用户ID={current_user_id} | 对话ID={conversation_id} | 消息长度={len(message)}")
        
        # 生成摘要（这里简单地截取前20个字符）
        summary = message[:20]
        
        # 查找并更新对话标题
        conversation = db.query(Conversation).filter(
            Conversation.id == conversation_id,
            Conversation.user_id == current_user_id
        ).first()
        
        if not conversation:
            raise HTTPException(status_code=404, detail="对话不存在或无权访问")
        
        # 更新对话标题和更新时间
        conversation.title = summary
        conversation.updated_at = datetime.utcnow()
        db.commit()
        
        # 返回响应
        return success(SummaryResponse(
            message=summary,
            original_length=len(message),
            conversation_id=conversation_id
        ))
        
    except HTTPException:
        # 重新抛出 HTTP 异常
        raise
    except Exception as e:
        logger.error(f"处理消息摘要请求时出错: {str(e)}")
        db.rollback()  # 确保在错误时回滚数据库事务
        raise HTTPException(status_code=500, detail="服务器内部错误")
