# routers/conversations.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

from app.models import Conversation, Message, HttpResult, error, success
from app.utils import get_current_user
import logging
from config import get_db

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/conversations", tags=["conversations"])

# 模型定义
class ConversationResponse(BaseModel):
    id: int
    title: str
    created_at: str
    updated_at: str
    message_count: int

class ConversationCreate(BaseModel):
    title: Optional[str] = "新对话"
    initial_message: Optional[str] = None

class ConversationUpdate(BaseModel):
    id: int  # 添加对话ID字段
    title: str

class ConversationDetail(BaseModel):
    id: int
    title: str
    updated_at: str

class DeleteResponse(BaseModel):
    success: bool
    message: str

# 获取所有对话
@router.get("/list", response_model=HttpResult[List[ConversationResponse]])
async def get_conversations(
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
        获取所有对话
    '''

    conversations = db.query(
        Conversation.id,
        Conversation.title,
        Conversation.created_at,
        Conversation.updated_at,
        func.count(Message.id).label('message_count')
    ).outerjoin(Message, Conversation.id == Message.conversation_id)\
     .filter(Conversation.user_id == current_user_id)\
     .group_by(Conversation.id)\
     .order_by(Conversation.updated_at.desc())\
     .all()

    result = []
    for conv in conversations:
        result.append({
            'id': conv.id,
            'title': conv.title,
            'created_at': conv.created_at.isoformat(),
            'updated_at': conv.updated_at.isoformat(),
            'message_count': conv.message_count
        })
    
    return success(result)

# 创建新对话
@router.post("/create", response_model=HttpResult[ConversationDetail])
async def create_conversation(
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    创建新对话
    '''
    try:
        new_conv = Conversation(
            user_id=current_user_id,
            title="新对话"
        )
        db.add(new_conv)
        db.commit()
        db.refresh(new_conv)

        # if conversation_data.initial_message:
        #     new_msg = Message(
        #         conversation_id=new_conv.id,
        #         role='user',
        #         content=conversation_data.initial_message
        #     )
        #     db.add(new_msg)
        #     db.commit()

        return success({
            'id': new_conv.id,
            'title': new_conv.title,
            'updated_at': new_conv.updated_at.isoformat()
        })

    except Exception as e:
        db.rollback()
        logger.error(f"创建对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建对话失败")

# 删除对话
@router.post("/remove/{conv_id}", response_model=HttpResult[str])
async def delete_conversation(
    conv_id: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    '''
    删除对话
    '''
     
    try:
        # 验证对话存在且属于当前用户
        conv = db.query(Conversation).filter(
            Conversation.id == conv_id,
            Conversation.user_id == current_user_id
        ).first()

        if not conv:
            raise HTTPException(status_code=404, detail="对话不存在或无权访问")
        
        # 先删除所有关联的消息
        db.query(Message).filter(Message.conversation_id == conv_id).delete()
        
        # 删除对话本身
        db.delete(conv)
        db.commit()
        
        return success("对话已删除")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除对话时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.post("/update", response_model=HttpResult[ConversationDetail])
async def update_conversation(
    conversation_update: ConversationUpdate,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    
    '''
    更新对话
    '''
    try:
        # 从请求体中获取对话ID
        conv_id = conversation_update.id  # 改为使用id字段
        if not conv_id:
            raise HTTPException(status_code=400, detail="对话ID不能为空")
            
        # 验证标题不为空
        new_title = conversation_update.title
        if not new_title or len(new_title.strip()) == 0:
            raise HTTPException(status_code=400, detail="标题不能为空")
            
        # 验证对话存在且属于当前用户
        conv = db.query(Conversation).filter(
            Conversation.id == conv_id,
            Conversation.user_id == current_user_id
        ).first()

        if not conv:
            raise HTTPException(status_code=404, detail="对话不存在或者无权访问")
            
        # 更新标题
        conv.title = new_title.strip()
        conv.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(conv)
            
        return success({
            "id": conv.id,
            "title": conv.title,
            "updated_at": conv.updated_at.isoformat()
        })
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新对话标题时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
