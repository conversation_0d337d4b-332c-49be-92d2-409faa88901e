#!/usr/bin/env python3
"""
执行 Worker 启动脚本
专门负责执行任务，不处理状态监听
"""

import os
import sys
import subprocess

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def start_executor_worker():
    """启动执行 Worker"""
    print("🚀 启动 Celery 执行 Worker...")
    
    # 设置环境变量
    os.environ.setdefault('CELERY_APP', 'app.tasks')
    # 明确标识这是执行 Worker，需要初始化模型
    os.environ['WORKER_TYPE'] = 'executor'
    
    # 启动 worker
    cmd = [
        'celery', '-A', 'app.tasks.celery_app', 'worker',
        '--loglevel=info',
        '--concurrency=2',  # 并发数
        '--queues=default',  # 监听默认队列
        '--hostname=executor@%h',  # Worker 主机名
        '--pidfile=/tmp/celery_executor.pid',  # PID 文件
        '--logfile=/tmp/celery_executor.log',  # 日志文件
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n🛑 执行 Worker 已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动执行 Worker 失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    start_executor_worker() 