@echo off
echo 启动 FunASR Celery Worker...
echo.

REM 激活虚拟环境（如果存在）
if exist ".venv\Scripts\activate.bat" (
    call .venv\Scripts\activate.bat
    echo 虚拟环境已激活
) else (
    echo 警告: 未找到虚拟环境，使用系统 Python
)

echo.
echo 🚀 启动带有 FunASR 模型预加载的 Celery Worker
echo ================================================
echo.
echo 注意: Worker 启动时会自动初始化 FunASR 模型
echo 这可能需要几分钟时间，请耐心等待...
echo.

REM 启动 Celery Worker
echo 正在启动 Celery Worker...
celery -A app.tasks.celery_app worker --loglevel=info --pool=solo --concurrency=1 -E

pause 