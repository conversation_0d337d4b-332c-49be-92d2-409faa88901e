import uvicorn

# 配置Uvicorn优化参数
UVICORN_CONFIG = {
    "host": "0.0.0.0",
    "port": 5001,
    "workers": 1,  # 增加工作进程数量
    # "loop": "uvloop",  # 使用uvloop性能更好
    "http": "httptools",  # 更快的HTTP解析
    "limit_concurrency": 1000,  # 限制并发连接数
    "backlog": 2048,  # 增加积压请求数量
    "timeout_keep_alive": 5,  # 减少保持连接的时间
}

# 启动代码
if __name__ == "__main__":
    print(f"启动Web服务器 - 配置: {UVICORN_CONFIG}")
    uvicorn.run(
        "app.main:app",
        **UVICORN_CONFIG
    )
