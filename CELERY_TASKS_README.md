# 音频转换任务使用说明

## 概述

本项目使用 Celery 来处理音频转换任务，支持异步处理音频文件的下载、格式转换和上传。

## 系统要求

1. **Python 3.11+**
2. **Redis 服务器** - 作为 Celery 的消息代理和结果后端
3. **相关 Python 包**：
   - celery>=5.3.0
   - redis>=5.0.0
   - requests>=2.32.3

## 安装和配置

### 1. 安装依赖

```bash
# 使用 uv 安装依赖
uv sync

```

### 2. 启动 Redis 服务器

确保 Redis 服务器正在运行，默认配置为：
- 地址：localhost
- 端口：6379
- 数据库：0

### 3. 配置

配置文件位于 `conf/develop.yaml`，包含以下 Celery 配置：

```yaml
CELERY:
  BROKER_URL: 'redis://localhost:6379/0'
  RESULT_BACKEND: 'redis://localhost:6379/1'
  TASK_SERIALIZER: 'json'
  RESULT_SERIALIZER: 'json'
  ACCEPT_CONTENT: ['json']
  TIMEZONE: 'Asia/Shanghai'
  ENABLE_UTC: True
  TASK_TRACK_STARTED: True
  TASK_TIME_LIMIT: 30 * 60  # 30分钟
  TASK_SOFT_TIME_LIMIT: 25 * 60  # 25分钟
```

## 启动服务

### 1. 启动 Celery Worker

Celery Worker 负责执行具体的任务，如音频转换。

```bash
# Windows
start_celery_worker.bat

# Linux/Mac
celery -A app.tasks.celery_app worker --loglevel=info
```

### 2. 启动 Celery Beat（可选）

Celery Beat 负责定时任务的调度，如清理临时文件、检查失败任务等。

```bash
# Windows
start_celery_beat.bat

# Linux/Mac
celery -A app.tasks.celery_app beat --loglevel=info
```

### 3. 启动 FastAPI 应用

```bash
# Windows
run-dev.bat

# Linux/Mac
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 任务功能

### 音频转换任务 (`convert_audio`)

**功能描述：**
- 下载音频文件
- 转换音频格式
- 上传转换后的文件
- 更新会议状态

**任务流程：**
1. 接收会议ID
2. 从数据库获取会议信息
3. 解析音频原始URL列表
4. 逐个处理音频文件：
   - 下载到临时目录
   - 转换格式
   - 上传到存储服务
5. 更新会议状态和文件信息

**状态说明：**
- `uploaded`: 已上传，等待处理
- `handing`: 处理中
- `finished`: 处理完成
- `failed`: 处理失败
- `canceled`: 已取消

### 定时任务

#### 1. 清理临时文件任务 (`cleanup_temp_files`)

**功能：** 清理系统临时目录中的音频文件
**执行频率：** 每24小时执行一次
**作用：** 防止临时文件占用过多磁盘空间

#### 2. 检查失败任务 (`check_failed_conversions`)

**功能：** 检查并重新触发失败的音频转换任务
**执行频率：** 每小时执行一次
**作用：** 自动重试失败的任务，提高系统可靠性

## API 接口

### 1. 创建会议（自动触发音频转换）

```http
POST /api/meeting/create
Content-Type: application/json

{
  "name": "会议名称",
  "audioOriginUrls": "[\"http://example.com/audio1.mp3\", \"http://example.com/audio2.mp3\"]",
  "audioState": "uploaded"
}
```

### 2. 查询音频转换状态

```http
GET /api/meeting/audio-status/{meeting_id}
```

响应示例：
```json
{
  "code": 200,
  "message": "操作成功!!!",
  "data": {
    "meeting_id": 1,
    "audio_state": "handing",
    "task_status": "PROGRESS",
    "progress": 50,
    "current_file": 2,
    "total_files": 4,
    "error_message": null
  }
}
```

## 自定义配置

### 音频格式转换

在 `app/tasks/audio_tasks.py` 中的 `convert_audio_format` 函数中实现具体的格式转换逻辑：

```python
def convert_audio_format(input_file_path: str, temp_dir: str) -> str:
    """
    转换音频格式
    """
    # 使用 ffmpeg 进行格式转换
    import subprocess
    
    output_filename = f"converted_{os.path.basename(input_file_path)}"
    output_file_path = os.path.join(temp_dir, output_filename)
    
    # 转换为 MP3 格式
    cmd = [
        'ffmpeg', '-i', input_file_path,
        '-acodec', 'libmp3lame',
        '-ab', '128k',
        output_file_path
    ]
    
    subprocess.run(cmd, check=True)
    return output_file_path
```

### 文件上传

在 `upload_audio_file` 函数中实现具体的文件上传逻辑：

```python
def upload_audio_file(file_path: str, meeting_id: int, file_index: int) -> str:
    """
    上传音频文件到存储服务
    """
    # 示例：上传到阿里云 OSS
    import oss2
    
    # 配置 OSS
    auth = oss2.Auth('AccessKeyId', 'AccessKeySecret')
    bucket = oss2.Bucket(auth, 'endpoint', 'bucket-name')
    
    # 上传文件
    filename = f"meeting_{meeting_id}_audio_{file_index}.mp3"
    bucket.put_object_from_file(filename, file_path)
    
    return f"https://bucket-name.oss-cn-region.aliyuncs.com/{filename}"
```

### 添加新的定时任务

在 `app/tasks/__init__.py` 中的 `beat_schedule` 配置中添加新的定时任务：

```python
celery_app.conf.beat_schedule = {
    'your-task-name': {
        'task': 'your_task_function',
        'schedule': 3600.0,  # 执行频率（秒）
        'args': (),          # 任务参数
    },
}
```

## 监控和日志

### 日志配置

任务执行日志会记录到控制台，包含以下信息：
- 任务开始和结束时间
- 文件处理进度
- 错误信息
- 状态更新

### 任务监控

可以使用 Celery 的监控工具：

```bash
# 查看任务状态
celery -A app.tasks.celery_app inspect active

# 查看任务统计
celery -A app.tasks.celery_app inspect stats

# 查看定时任务
celery -A app.tasks.celery_app inspect scheduled
```

## 故障排除

### 常见问题

1. **Redis 连接失败**
   - 检查 Redis 服务是否启动
   - 验证配置文件中的连接地址

2. **任务执行失败**
   - 检查日志中的错误信息
   - 验证音频文件URL是否可访问
   - 确认存储服务配置正确

3. **内存不足**
   - 调整 `TASK_TIME_LIMIT` 和 `TASK_SOFT_TIME_LIMIT`
   - 考虑使用流式处理大文件

4. **定时任务不执行**
   - 确保 Celery Beat 正在运行
   - 检查 `beat_schedule` 配置是否正确
   - 验证任务函数名称是否正确

### 性能优化

1. **并发处理**
   - 调整 Worker 数量：`celery -A app.tasks.celery_app worker --concurrency=4`
   - 使用进程池：`--pool=prefork`

2. **文件处理**
   - 使用流式下载避免内存溢出
   - 及时清理临时文件

3. **数据库连接**
   - 使用连接池
   - 及时关闭数据库连接

## 扩展功能

### 添加新的音频处理任务

1. 在 `app/tasks/audio_tasks.py` 中定义新任务
2. 在 `app/tasks/__init__.py` 中导入任务
3. 在 API 中调用任务

### 定时任务配置

定时任务在 `app/tasks/__init__.py` 中的 `beat_schedule` 配置：

```python
celery_app.conf.beat_schedule = {
    'cleanup-temp-files': {
        'task': 'cleanup_temp_files',
        'schedule': 86400.0,  # 每24小时执行一次
    },
    'check-failed-conversions': {
        'task': 'check_failed_conversions',
        'schedule': 3600.0,   # 每小时执行一次
    },
}
```

### 开发环境快速启动

使用提供的批处理脚本快速启动开发环境：

1. `start-dev-env.bat` - 启动 Redis 和 MySQL 服务
2. `start_celery_worker.bat` - 启动 Celery Worker
3. `start_celery_beat.bat` - 启动 Celery Beat 调度器（可选）
4. `run-dev.bat` - 启动 FastAPI 应用 