#!/usr/bin/env python3
"""
测试 Worker 分离的脚本
验证执行 Worker 和监听 Worker 是否正确分离
"""

import os
import sys
import subprocess
import time
import signal

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_executor_worker():
    """测试执行 Worker 启动"""
    print("🧪 测试执行 Worker 启动...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['WORKER_TYPE'] = 'executor'
    env['CELERY_APP'] = 'app.tasks'
    
    cmd = [
        'celery', '-A', 'app.tasks.celery_app', 'worker',
        '--loglevel=info',
        '--concurrency=1',
        '--queues=test',
        '--hostname=test_executor@%h',
        '--time-limit=30',  # 30秒后自动停止
    ]
    
    try:
        process = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待一段时间让 Worker 启动
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 执行 Worker 启动成功")
            
            # 检查输出中是否包含模型初始化信息
            stdout, stderr = process.communicate(timeout=10)
            
            if "FunASR 模型初始化完成" in stdout or "FunASR 模型初始化完成" in stderr:
                print("✅ 执行 Worker 正确初始化了 FunASR 模型")
            else:
                print("⚠️ 执行 Worker 可能没有初始化模型")
                
            process.terminate()
            process.wait(timeout=5)
        else:
            print(f"❌ 执行 Worker 启动失败，退出码: {process.returncode}")
            stdout, stderr = process.communicate()
            print(f"错误输出: {stderr}")
            
    except Exception as e:
        print(f"❌ 测试执行 Worker 时发生错误: {e}")

def test_monitor_worker():
    """测试监听 Worker 启动"""
    print("🧪 测试监听 Worker 启动...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['WORKER_TYPE'] = 'monitor'
    env['CELERY_APP'] = 'app.tasks.monitor'
    
    cmd = [
        'celery', '-A', 'app.tasks.monitor.monitor_app', 'worker',
        '--loglevel=info',
        '--concurrency=1',
        '--queues=test',
        '--hostname=test_monitor@%h',
        '--time-limit=30',  # 30秒后自动停止
    ]
    
    try:
        process = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待一段时间让 Worker 启动
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 监听 Worker 启动成功")
            
            # 检查输出中是否包含跳过模型初始化的信息
            stdout, stderr = process.communicate(timeout=10)
            
            if "检测到监听 Worker，跳过模型初始化" in stdout or "检测到监听 Worker，跳过模型初始化" in stderr:
                print("✅ 监听 Worker 正确跳过了模型初始化")
            elif "FunASR 模型初始化完成" in stdout or "FunASR 模型初始化完成" in stderr:
                print("❌ 监听 Worker 错误地初始化了模型")
            else:
                print("⚠️ 监听 Worker 输出中没有明确的模型初始化信息")
                
            process.terminate()
            process.wait(timeout=5)
        else:
            print(f"❌ 监听 Worker 启动失败，退出码: {process.returncode}")
            stdout, stderr = process.communicate()
            print(f"错误输出: {stderr}")
            
    except Exception as e:
        print(f"❌ 测试监听 Worker 时发生错误: {e}")

def main():
    """主测试函数"""
    print("🧪 开始测试 Worker 分离...")
    print("=" * 50)
    
    # 测试执行 Worker
    test_executor_worker()
    print("-" * 30)
    
    # 测试监听 Worker
    test_monitor_worker()
    print("-" * 30)
    
    print("🧪 Worker 分离测试完成")

if __name__ == '__main__':
    main() 