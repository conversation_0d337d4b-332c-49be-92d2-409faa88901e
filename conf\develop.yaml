DEFAULT:
  # 无需登录API
  WHITE_API_LIST:
    - 'login'
    - 'verify-code'
    - 'register'
    - 'reset-password'

  # 存储配置
  STORAGE:
    MYSQL:
      URI: 'mysql+pymysql://dev1:dev12025@192.168.110.250:3308/meeting-ai?charset=utf8mb4'
  
  # 阿里云OSS配置
  OSS:
    ACCESS_KEY_ID: 'LTAI5t8cR94GGDoX73Sy6tsi'
    ACCESS_KEY_SECRET: '******************************'
    ENDPOINT: 'oss-cn-chengdu.aliyuncs.com'  # 根据您的region修改
    BUCKET_NAME: 'sskwmeeting'
    REGION: 'cn-chengdu'  # 根据您的region修改
    EXPIRE_TIME: 3600  # 上传凭证过期时间（秒）
    MAX_SIZE: 304857600  # 最大文件大小（100MB）

  # Celery 配置
  CELERY:
    BROKER_URL: 'redis://localhost:6379/0'
    RESULT_BACKEND: 'redis://localhost:6379/1'
    ACCEPT_CONTENT: ['json']
    TIMEZONE: 'Asia/Shanghai'
    ENABLE_UTC: True
    TASK_TRACK_STARTED: True
    TASK_TIME_LIMIT: 30 * 60  # 30分钟
    TASK_SOFT_TIME_LIMIT: 25 * 60  # 25分钟
  
  USER:
    HOST: 'https://meeting.sensearray.com/usercenter'
  
  FUNASR:
    BATCH_SIZE_S: 300
    MODEL: 'paraformer-zh'
    VAD_MODEL: 'fsmn-vad'
    PUNC_MODEL: 'ct-punc'
    SPK_MODEL: 'cam++'

  CACHE:
    DIR: './cache'
    CLEAR_AFTER_USED: True

  # 提示配置
  MESSAGE:
    SUCCESS: '操作成功!!!'

  # 验证码过期时间(秒)
  VERIFY_CODE_EXPIRE: 300
  JWT_ACCESS_TOKEN_EXPIRE_DAYS: 30

  JWT_SECRET_KEY: 'django-insecure-x5&+mzz(eomnqq91@s+z30jc2u@o9_f-3t&h)%_-c6n=#q4^+'
  JWT_ALGORITHM: 'HS256'
  
  # 格式设置
  FORMAT:
    DATE: '%Y-%m-%d'
    DATETIME: '%Y-%m-%d %H:%M:%S'
    TIME: '%H:%M:%S'