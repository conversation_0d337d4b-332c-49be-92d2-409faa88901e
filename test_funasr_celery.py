#!/usr/bin/env python3
"""
测试 FunASR Celery 任务
演示如何在 Celery Worker 启动时初始化模型
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_funasr_celery():
    """测试 FunASR Celery 功能"""
    print("🧪 测试 FunASR Celery 功能")
    print("=" * 50)
    
    try:
        # 导入 Celery 应用
        from app.tasks import celery_app
        from app.tasks.funasr import transcribe_audio_task
        
        print("✅ Celery 应用导入成功")
        
        # 检查模型是否已初始化
        print("\n🔍 检查模型状态...")
        # model = get_or_init_model()
        # if model:
        #     print("✅ FunASR 模型已初始化")
        # else:
        #     print("❌ FunASR 模型初始化失败")
        #     return False
        
        # 测试音频文件路径（这里需要替换为实际的音频文件路径）
        test_audio_file = "1.mp4"  # 请替换为实际的音频文件路径
        
        if not os.path.exists(test_audio_file):
            print(f"\n⚠️ 测试音频文件不存在: {test_audio_file}")
            print("💡 请将测试音频文件放在项目根目录下，或修改脚本中的文件路径")
            return False
        
        print(f"\n🎵 开始测试音频转写: {test_audio_file}")
        
        # 提交 Celery 任务
        task = transcribe_audio_task.delay(1, [test_audio_file])
        print(f"📋 任务已提交，ID: {task.id}")
        
        # 等待任务完成
        print("⏳ 等待任务完成...")
        result = task.get(timeout=300)  # 5分钟超时
        
        print(result)
        
        if result:
            print("✅ 音频转写任务完成!")
            print(f"📊 转写结果:")
            print(f"   - 文件: {result.get('file_name', 'N/A')}")
            print(f"   - 大小: {result.get('file_size_mb', 0):.2f} MB")
            print(f"   - 转写时间: {result.get('statistics', {}).get('transcription_time', 0):.2f}秒")
            print(f"   - 说话人数量: {len(set([s['speaker'] for s in result.get('speaker_segments', [])]))}")
            print(f"   - 文本段落数: {len(result.get('speaker_segments', []))}")
            
            # 显示前几段转写结果
            segments = result.get('speaker_segments', [])
            if segments:
                print(f"\n📝 转写内容预览:")
                for i, segment in enumerate(segments[:3]):  # 只显示前3段
                    print(f"   [{segment['start_time']:.1f}s-{segment['end_time']:.1f}s] "
                          f"说话人{segment['speaker']}: {segment['text']}")
                if len(segments) > 3:
                    print(f"   ... 还有 {len(segments) - 3} 段内容")
        else:
            print("❌ 音频转写任务失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_model_initialization():
    """测试模型初始化"""
    print("\n🧪 测试模型初始化")
    print("=" * 30)
    
    try:
        from app.tasks.funasr import init_model, get_or_init_model
        
        # 测试直接初始化
        print("🔧 测试直接初始化模型...")
        model = init_model()
        if model:
            print("✅ 模型直接初始化成功")
        else:
            print("❌ 模型直接初始化失败")
            return False
        
        # 测试获取或初始化
        print("🔧 测试获取或初始化模型...")
        model = get_or_init_model()
        if model:
            print("✅ 获取或初始化模型成功")
        else:
            print("❌ 获取或初始化模型失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模型初始化测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 FunASR Celery 测试脚本")
    print("=" * 60)
    
    # 测试模型初始化
    # if not test_model_initialization():
    #     print("\n❌ 模型初始化测试失败，跳过 Celery 测试")
    #     return
    
    # 测试 Celery 功能
    if test_funasr_celery():
        print("\n🎉 所有测试通过!")
    else:
        print("\n❌ 测试失败")
    
    print("\n📋 使用说明:")
    print("1. 确保 Redis 服务正在运行")
    print("2. 启动 Celery Worker: python celery_worker.py")
    print("3. 在另一个终端运行此测试脚本")
    print("4. 或者直接调用 transcribe_audio_task.delay(audio_file_path)")

if __name__ == "__main__":
    main() 