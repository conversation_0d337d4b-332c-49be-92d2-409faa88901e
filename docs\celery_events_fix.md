# Celery 事件监控修复说明

## 问题描述

监听 Worker 没有接收到任务信号，日志中显示：
```
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
```

## 问题原因

Celery 的信号监听功能需要启用任务事件监控才能正常工作。默认情况下，Worker 不会启用事件监控，因此无法接收到任务相关的信号。

## 解决方案

### 1. 启用任务事件监控

在启动监听 Worker 时添加 `-E` 参数：

```bash
celery -A app.tasks.monitor.monitor_app worker --loglevel=info -E
```

### 2. 修改启动脚本

已更新以下启动脚本：

- `scripts/start_monitor_worker.py`
- `scripts/start_all_workers.py`

在监听 Worker 的启动命令中添加了 `-E` 参数。

### 3. 信号选择

根据实际需求选择合适的信号：

- `task_sent`: 任务发送到队列时触发
- `task_started`: 任务开始执行时触发
- `task_success`: 任务成功完成时触发
- `task_failure`: 任务失败时触发
- `task_postrun`: 任务执行完成后触发（无论成功还是失败）

## 修复内容

### 1. 启动脚本修改

```python
# 修改前
cmd = [
    'celery', '-A', 'app.tasks.monitor.monitor_app', 'worker',
    '--loglevel=info',
    '--concurrency=1',
    '--queues=default',
    '--hostname=monitor@%h',
]

# 修改后
cmd = [
    'celery', '-A', 'app.tasks.monitor.monitor_app', 'worker',
    '--loglevel=info',
    '--concurrency=1',
    '--queues=default',
    '--hostname=monitor@%h',
    '-E',  # 启用任务事件监控，这是信号监听的关键
]
```

### 2. 信号处理修改

```python
# 使用 task_sent 信号监听任务发送
from celery.signals import task_sent

@task_sent.connect
def task_sent_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kw):
    """任务发送到队列时的处理"""
    # 处理逻辑
```

### 3. 状态更新逻辑

```python
def update_task_status_in_db(task_id: str, state: str, result=None):
    if state == 'PENDING':
        meeting.audioState = "uploaded"
        meeting.celeryTaskStatus = "pending"
    elif state == 'STARTED':
        meeting.audioState = "handing"
        meeting.celeryTaskStatus = "running"
    # ... 其他状态
```

## 验证方法

### 1. 启动监听 Worker

```bash
# 使用修改后的脚本
python scripts/start_monitor_worker.py

# 或者直接使用命令
celery -A app.tasks.monitor.monitor_app worker --loglevel=info -E
```

### 2. 检查日志

启动后应该看到：
```
-- ******* ---- .> task events: ON (enable -E to monitor tasks in this worker)
```

### 3. 运行测试

```bash
# 测试任务发送监听
python test_task_sent_monitor.py

# 测试任务开始监听
python test_task_started_monitor.py
```

## 注意事项

1. **性能影响**: 启用事件监控会增加一些性能开销，但对于监听 Worker 来说是可以接受的
2. **网络开销**: 事件监控会产生额外的网络通信
3. **内存使用**: 事件监控会占用一些内存来存储事件信息

## 故障排除

### 1. 仍然看不到事件

- 确认使用了 `-E` 参数
- 检查 Worker 日志中是否显示 `task events: ON`
- 确认监听 Worker 和执行 Worker 使用相同的 broker

### 2. 信号回调不触发

- 检查信号导入是否正确
- 确认回调函数装饰器使用正确
- 查看是否有异常被捕获但未记录

### 3. 数据库状态不更新

- 检查数据库连接
- 查看 `update_task_status_in_db` 函数的错误日志
- 确认任务ID匹配正确 