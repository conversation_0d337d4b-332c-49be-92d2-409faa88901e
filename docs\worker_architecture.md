# Celery Worker 分离架构说明

## 🏗️ 架构概述

本项目采用分离的 Celery Worker 架构，将任务执行和状态监听分离到不同的 Worker 中，提高系统的可扩展性和稳定性。

## 📊 架构图

```
业务服务器
    ↓
任务队列 (Redis/RabbitMQ)
    ↓
┌─────────────────┬─────────────────┐
│   执行 Worker   │   监听 Worker   │
│  (Executor)     │   (Monitor)     │
│                 │                 │
│ - 执行任务      │ - 监听状态      │
│ - 处理业务逻辑  │ - 更新数据库    │
│ - 无状态监听    │ - 定时清理      │
└─────────────────┴─────────────────┘
    ↓                    ↓
结果存储 (Redis)     数据库更新
```

## 🔧 组件说明

### 1. 执行 Worker (`app/tasks/__init__.py`)

**职责：**
- 执行具体的任务（音频转换、转写等）
- 处理业务逻辑
- 不负责状态监听和数据库更新

**特点：**
- 应用名称：`meeting_api_executor`
- 专注于任务执行效率
- 可独立扩展并发数

### 2. 监听 Worker (`app/tasks/monitor.py`)

**职责：**
- 监听任务状态变化
- 更新数据库中的任务状态
- 处理任务成功/失败的业务逻辑
- 定时清理旧任务记录

**特点：**
- 应用名称：`meeting_api_monitor`
- 包含信号监听器
- 定时任务调度

## 🚀 启动方式

### 方式一：分别启动（推荐用于开发）

```bash
# 启动执行 Worker
python scripts/start_executor_worker.py

# 启动监听 Worker
python scripts/start_monitor_worker.py

# 启动监听 Worker 的定时任务调度器
python scripts/start_monitor_worker.py --beat
```

### 方式二：统一启动（推荐用于生产）

```bash
# 启动所有 Worker
python scripts/start_all_workers.py
```

### 方式三：直接使用 Celery 命令

```bash
# 启动执行 Worker
celery -A app.tasks.celery_app worker --loglevel=info --concurrency=2

# 启动监听 Worker
celery -A app.tasks.monitor.monitor_app worker --loglevel=info --concurrency=1

# 启动监听 Worker 的定时任务调度器
celery -A app.tasks.monitor.monitor_app beat --loglevel=info
```

## 📋 配置说明

### 执行 Worker 配置

```python
# app/tasks/__init__.py
celery_app = Celery(
    'meeting_api_executor',
    broker=cfg.CELERY.BROKER_URL,
    backend=cfg.CELERY.RESULT_BACKEND,
    include=['app.tasks.audio_tasks', 'app.tasks.funasr']
)
```

### 监听 Worker 配置

```python
# app/tasks/monitor.py
monitor_app = Celery(
    'meeting_api_monitor',
    broker=cfg.CELERY.BROKER_URL,
    backend=cfg.CELERY.RESULT_BACKEND,
    include=['app.tasks.monitor']
)
```

## ⏰ 定时任务

### 执行 Worker 定时任务

```python
celery_app.conf.beat_schedule = {
    'cleanup-temp-files': {
        'task': 'cleanup_temp_files',
        'schedule': 86400.0,  # 每24小时执行一次
    },
    'check-failed-conversions': {
        'task': 'check_failed_conversions',
        'schedule': 3600.0,   # 每小时执行一次
    },
}
```

### 监听 Worker 定时任务

```python
monitor_app.conf.beat_schedule = {
    'monitor-pending-tasks': {
        'task': 'monitor_pending_tasks',
        'schedule': 30.0,  # 每30秒检查一次待处理任务
    },
    'cleanup-old-tasks': {
        'task': 'cleanup_old_tasks',
        'schedule': 3600.0,  # 每小时清理一次旧任务
    },
}
```

## 🔄 信号处理流程

### 任务执行流程

1. **任务提交** → 业务服务器提交任务到队列
2. **任务执行** → 执行 Worker 从队列获取任务并执行
3. **状态监听** → 监听 Worker 通过信号监听任务状态变化
4. **数据库更新** → 监听 Worker 更新数据库中的任务状态
5. **业务逻辑处理** → 监听 Worker 处理任务成功/失败的业务逻辑

### 信号触发时机

- `task_success`：任务成功完成时触发
- `task_failure`：任务执行失败时触发
- `task_postrun`：任务执行完成后触发（无论成功失败）

## 🛠️ 监控和维护

### 日志文件

- 执行 Worker：`/tmp/celery_executor.log`
- 监听 Worker：`/tmp/celery_monitor.log`
- 定时任务调度器：`/tmp/celery_monitor_beat.log`

### PID 文件

- 执行 Worker：`/tmp/celery_executor.pid`
- 监听 Worker：`/tmp/celery_monitor.pid`
- 定时任务调度器：`/tmp/celery_monitor_beat.pid`

### 进程管理

```bash
# 查看 Worker 状态
celery -A app.tasks.celery_app inspect active
celery -A app.tasks.monitor.monitor_app inspect active

# 停止 Worker
celery -A app.tasks.celery_app control shutdown
celery -A app.tasks.monitor.monitor_app control shutdown
```

## 🎯 优势

### 1. 职责分离
- 执行 Worker 专注于任务执行
- 监听 Worker 专注于状态管理
- 避免单点故障

### 2. 可扩展性
- 可独立扩展执行或监听能力
- 根据负载调整并发数
- 支持多实例部署

### 3. 稳定性
- 监听服务故障不影响任务执行
- 自动重启机制
- 完善的错误处理

### 4. 维护性
- 代码结构清晰
- 便于调试和监控
- 独立的日志和配置

## ⚠️ 注意事项

1. **数据库连接**：监听 Worker 需要稳定的数据库连接
2. **队列配置**：确保两个 Worker 监听相同的队列
3. **定时任务**：避免重复的定时任务配置
4. **资源分配**：合理分配 CPU 和内存资源
5. **监控告警**：设置适当的监控和告警机制
6. **模型初始化**：只有执行 Worker 会初始化 FunASR 模型，监听 Worker 不会

## 🔧 模型初始化分离

### 问题背景
在分离 Worker 架构中，监听 Worker 不应该加载 FunASR 模型，因为：
- 监听 Worker 只负责状态监听，不需要模型
- 模型加载会消耗大量内存和 CPU 资源
- 避免不必要的资源浪费

### 解决方案
通过环境变量 `WORKER_TYPE` 来区分 Worker 类型：

```python
# 执行 Worker 初始化
@worker_ready.connect
def worker_init(sender, **kwargs):
    worker_type = os.environ.get('WORKER_TYPE', 'executor')
    if worker_type == 'monitor':
        print("🔍 检测到监听 Worker，跳过模型初始化")
        return
    
    # 只在执行 Worker 中初始化模型
    from app.tasks.funasr import get_or_init_model
    get_or_init_model()
```

### 环境变量设置

**执行 Worker：**
```bash
export WORKER_TYPE=executor
celery -A app.tasks.celery_app worker
```

**监听 Worker：**
```bash
export WORKER_TYPE=monitor
celery -A app.tasks.monitor.monitor_app worker
```

### 测试验证
使用测试脚本验证 Worker 分离是否正确：

```bash
python scripts/test_worker_separation.py
```

测试脚本会验证：
- 执行 Worker 正确初始化模型
- 监听 Worker 跳过模型初始化
- 两个 Worker 都能正常启动 